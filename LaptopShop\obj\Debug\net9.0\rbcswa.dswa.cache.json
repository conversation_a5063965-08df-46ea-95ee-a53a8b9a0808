{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["J4EgphiFmdaPTgyASJJNd2agdgu/hb4dI91bEF4vJaM=", "ORtqfI1+XJDrndtL6ZqPKNVkyKae9TAxONc0ok5M5G4=", "NGEWRrmKTLhMo5BFSTtoomjvOVVfFQqY/WOLFw38Nxc=", "reiRraPhwWNvuDE0iQa++8j8mEe/W2aRtaWpXpePe8I=", "qdFKpiFCos+XFXxbRKms+DDg0uIcoa/tMmoL7Z+tW5k=", "fuV+KoPOyRpbq65AyofkzWZxwD7eVuszqYrTco85EGc=", "pS5C7Ef24FrMRjhqflu1eu4svNZvqjb7ANMh4TN61+U=", "lZoFg5bWjFTmN4c4E3MijM+IDBKUqDASH0vJCSMupN8=", "ZWSBMFgKXTMUeYhdD5/JGJrkzKeOehIag9VQpU+c/z8=", "mdhtb7hKBbRQCIv6np6SULapB3OPW7IAhr/7carAJQY=", "+tya5s5hIfTlnrZVf8OaN1C4mu/4iz7dCZu58PB8sGA=", "Yl910uiI2bcs5vC5KQ6ApH3SwoqXvAqPZ9CT97NRvlM=", "nCpCSGX8z591Zb3LdNqTeIpKl5Tud5VJEVhEMqJYTUo=", "OhJK3zNcC6SoL9UXZU571CxRVh4qX1M1ELnP+zRjqBg=", "TdU9CHtJKxw3XQWYhfWcszP8SCSi0OODlO71AdCOkZE=", "mdPdYI8iR+nx9g/hVHbbIEdgvfLx3eS073nVLwdUjFQ=", "RBW5k4g9VfG5U9W0p6gCVM3aS2dMK2sZw5go7qGsa9o=", "Mz7kaGvTW4MbRUd37bwMnU2DnnAifynKvrmJ+KCSsTI=", "GjEPGeGQLjR7t2qP8BXdg/kL7XvEcL3vYyhqaDkdxkc=", "91VCy8+QwQ6neSQEelfBVuU3tcFq6Mx61/WqeDmrGc4=", "SI5Bxzso5xP3NnMeQ3m40vz06OaZ4YmQSzbOxdjmtRk=", "4KQOM6pSVy/XPm9+rCWdz6cDopO2RER/JpOUrXcOkFk=", "raJUNqVCpbuN5JuQJwxyK2lnTN1Amt6xqqYYhrMo4lU=", "CMA2B8mXXT9YtqtXv6xJy9QmOMl9urzqQhaGstFX3BA=", "higtnSAg67URMorMXawg/rTYQHKTMPZshg5XwAue4x0=", "eMW0qpik7p8CdECggMc79TaOTbgtLPJfdmYlinT7t5M=", "E9nidbXcpv3UDB642UAELu2neAoL4gef0fETXiNbxzg=", "b5Fe+bU1Wo13QI/SkGvTlnoMsuP+YiYrhPqN7rgjkHk=", "g696qRyV865HL9441B1/EiPzGWWjPOEs8sfp6T5Ci4o=", "92V1wmS3Rk1ndjCFxtlIRxg3wLO8wbdKiavPTEFFADI=", "t2zl+tJ98T7mIaTOT2JKH73udHvywee9KjOB88g5JzA=", "vVzXBSWh4NUwhd42pPx9VvDopTBvvILFvmQZifzyXqM=", "oVhoFiIUNmWPREwKIA5QolrvwclsVovsylfWUFPjH+o=", "ap/o5nbx3N5+UunisMTM4wadPYkdsnaJ6hklEtZcSL8=", "FdJ8mhiK9O439bCDlxgQObkqEt51d2dc0Jkd+HcESd8=", "rUijvWNtvD1ai+UQJDx/2LdGMTaXZwu8VdejqAaNWeI=", "j5by2Pu79dLVrO2mL1n+v+I/vRpeYSern6JjyLQHRWE=", "fZLL+zhC/alhXHmmwjLcT2IZ+jYlmmNVhhYXG3+4veU=", "cMtnka2856U3dQyN0lxi2UcnXabXpBwy5qlnYmJS1FY=", "lKW9naUP2PAI/0NnECbZWRjR+NYFSRfbLi8AIyVFUxc=", "9HNV0k+jpOp8DZ11UXd99eDnZTCzbwPyiQhfmIDwUp4=", "AuTghBl9zv7lmn2PfpzDnGi4r+jPC+/iERUdSA5qKr0=", "Xk1K5VUql+ZDZSwdYgW1gzKWMAwZbTjOs+j7YD3fy34=", "FL/nloeURiUVXIhuqk3NAVdDbtx6KlHMBPJ2q/WmsFs=", "ntXuV//78D7XMK8R5BVtdvKDwXKYyhA6N+lUUZjBsyA=", "e2YYlhrC389NZ22ZGpKXTRsW7cZ6YdtgRtlWJJUCGaA=", "Z2XjPWFp1qr060kSwKSzt2E+5Aded17nZJvNx4T+zEM=", "Ho6DLdk0Tbva9c7WuuyQjhAO1iGD0o7bhAvbJxNCFA8=", "dEFlxkU8amGYCKddOncNWejqSMA9ZCxiRlPoOCqKJLs=", "Qtu8qOKigRTBLqf+aHWTh1fJwaiX4N7UFyUxLKnpClE=", "Wv4AfpI0POs3TmExGoq560C6xuCqP5ndauxSXa8yJW4=", "YdbMRijkBNN0f5/mbp8aN5ivB8ifrCfxSbaIlPD8oqw=", "QGUDgzC+9R9AT9IQZTlk5Veg8bjAGt61mGjm1aSA9fw=", "/juJmi/AK/hfqN7PpMt7pbutoApZhtxASOhHa+bKSTU=", "Cwtd57mpI4thWgDoKMP7XAgMRlYGDhiuOidBpTM78GA=", "xoEo/wKf82sEQtm3lTPj5kQddZcKS09WsEvTIjwXMcU=", "lEjBA7MYnFV5xirvVaCPCGkNoerdu6TQ6uicHkhbtrc=", "66s8RHucVzbeB/Yw5wWKWRfdUgh3WV4YXkcg5kfzJ6U=", "jiED3HSR6SoEuaIQAI5kAvuA0mNu63alTNO/lWrIwDg=", "oKP5COd2xi/xBOipj1LYUpByYJE8GEXkZgBIjJ0O2kk=", "51WnQGommA48/APA7ZQBTOAV4SqTVLFCMuCv1jXn9sI=", "vJshXJD7C0i35Qq9AdkpCH+o0ua0lkOrti1yIGTeqY4=", "yt79HlcCf3PencA/JwmAt+90trhLiA5/SArjQ2QegL4=", "oYLerf1xMdWdI8nidZvkn6NnJ/N8GUSMcKzJmM5R5e0=", "U38aAhajd3EeImmvuzJahN+WoyBPQVXadeI2d024eoU=", "Gn64YMlFZBhrOI9BAcC3LznFhebSABFYpFKAv8fgihA=", "+Zc9tdSHiev0/lD8m3KSdnkQS7oz6gnKMioDSAdODEI=", "n0OV5C/KWLbIEW0mbMITy3MAZ1Q+GEG1gkGYKvR16Hw=", "xFaELH9b5Zq9pEYXSsVQeu9YI2UvZcctz8FOmJzynA4=", "r9UkTZhcW8yc7RHcMWzHaa1EYRozzEKdIty0Ug7iEJU=", "KjWpmEuhL/RYRuYZlzehoTPuyqfeHw9tdDPQukKXWUI=", "q0yU+pDfuUCGWvqNdjoGGIndJsqlqMueW1rGHmcMEj0=", "7qQKhX2qQcNGR15NEQVWgQlTgy9DDyYrAvpt7J9nnUU=", "1zBfWoeAGm71Ek9DEJxb2WnGBNCsFZhE68Dewo4mbe4=", "DVw/DH34bHy9B4UA4TVxL/FG2mwQAjsMDJ8lczpNWxs=", "H8QA+Z54LkRtxBkwZFakuZZY0EEM2iviVyZYSL/8bDI=", "SxWqRU/QXUTKTH/QRk3ZJcFPTFb6hqj9472T+gL4pZE=", "oeqLRQEVRYWVy2RZcKVSULzHdoM61Kr0WHrkb5zRXXQ=", "1NcaEbGtbKalOk+8x4Q1X9hB/qkRnuNv/mNwHHaB2vU=", "5ujbyOor05nohMItyQhYnyp1/FajCNuc5NsKmkGBSY4=", "dcqojcGnWKTE/zH2gV3JPRwGvYiwv3wPShUQeRvb3Ms=", "GZ7pLtYCAM7JpVS5PJS+xPEtLH5l4djenxuwAnXaJd4=", "guNTrvxvG8xYtPGcNfc3q0soPGtJsvxSZdWx0ns8kyQ=", "cECPDYVtsxvOipLnswpCiXeDA2mcMA0V+NF3mSvM/KQ=", "SgFD60aBYjNyG9XX4lWrG0p4JDeBWXlQf9Iolp2IliI=", "VE82T6+d5dRBPbVsY5AFX4c/ir/flclzjeibjwSIvyM=", "53Zhy18xeNPPennxHIM1en0tNoPTokWRhptIeN6zUnI=", "7mLiOU3hxA5gcI+pZH2+xVEL+FWgJZglaMc1uV+vNu0=", "JxBDH4pT9pXWm+JZg6wi1gjJL0LdmgrvArrR3w57hzw=", "E8siw4CU/4Fwtg69BwqunoQZlQm520lPj2sPwLdJS08=", "GjMnqIOgpWn2/tPxAoHl47hZnd6YUQOSqCr7H18DvwM=", "7kRn4wH2zLTTLm9Bz97pfsm6szpBPX1xZZrXKn5O73A=", "lFe9j5J1uC4tCvBsN9944auPpv7kEA76Y/Esn8e4Zn0=", "hYkUQRJwyefzhwz5sLoJnKb7Vi4sZvQvpsd94FW8kg4=", "2GWjAWB+s+m/jZEMcC2KLIpxeXW3mDiOfRyPpLCy6yE=", "7+Qz5G7p9D09nMWyBPu7mNn8RHPr+namR7Yy8K9KkOk=", "eF7So+W2/Q/Ycjf0WFv3AaJj7oQTuw1F5NqAbCGKvoo=", "wpJodjN7xAoYs2mZ0Qoj3NiUKu7HIwSu2lxNOXwi6Tg=", "oyX38JQvq8j+ahD+5553XOA0i1dOLyUW+3nVkifSTCM=", "n8Lz8gDVcT0FXolJuL5LRX/VtWiGomXS1AjQt6jsj/U=", "ss/3KW26Ol/b+ypWeMn4G65CVNCx81A0WPZ97shANZk=", "vgra6yf+KuU/84fqGxjyCf6yq9Pc8inrJY9knu9FVH8=", "MHdyxCTLtu5R62RRxCCBV4MVgQjYSat+LFAAaU0Pj9Y=", "V1wkXkLNZ7ckqT0T15uEsEdvSPOExy0a/Jl90epNy98=", "lzuUr9Y3Snzwg2Rw5Xj80Wtv5DTRK52ove9PEoyjfUU=", "e3qUoNwHlAnvXksoBvG7DDHSHeJuuTrN/KYvOympmQM=", "px8U/MKWbZocPV2ErtfLuBzFoKaTHiDAvZosk3LuboY=", "c1v4tW7s3Ls7Nn5LusKiNgd8uMPDbw91OqtDZJRzAbo=", "xzGEzMbkqyoM8fFB3lVLYp4FLw8M9eCPzDLgvISFBKs=", "WabUfCNNs8CGmtBM/XCHIEaVNii9gNf/dxx4S5qIzF4=", "S4PiSIv9rZBNKR4cYCZCxXpgydg8vz8knLlH1nlv3gs=", "9Zerb5zmjJD0XIVL06O1eqcm8T5iKNTEJqXHCWTV07A=", "EGCpCfA/0TzJ5mIOKhSu5ADzMBukBGRERCACM3b0kVE=", "lL+JilXl2Z1L7rGx/jvPOKhc9hnAnYuyWv30/TrZan4=", "LMqel3tdbtTgS6vv4MWuZX997eMhu8wHkyaN8RdAoMc=", "pJ1J0RBQkGm1rL6W/QtJ5LSWMzxdmuNcLu+M7tB2t9o=", "jaJ+ddWzJmmWhqU/JflsOoZQBtGDJC7ODCvCeipV0Ko=", "/4Fz6U5j/nLaHMsselA9UEszoVNCzwLkzRoq/7HFr2I=", "uG34zj+bQY6Wv5vtFAB3ozt9ByGoj1fy6yiN3iHnyFQ=", "WMt7akRTndZFf4a+bS0SS34SiUcusXFBnv6xionEYrQ=", "ija2pS/JFOoQCB/VfKSILFkdFYVu7M2/Wpe94pcujUE=", "6GEFEy4XWH3cAd1pALrW5xTrVAIWAHHsrKRA0rIiY00=", "Lh9Q9/fsldhG4lySwdrv23hp17GxVPm+SOLMMx7ahG8=", "8NaL4gUg8cSQYwQpioxk2uCTbem8jbL8QhzIn28Ywgw=", "N1igSzTCSoQVC3c5TS8A6HNYhg7Nrw1D9xJ8z4V8SBw=", "+NcSVD6w9ZjaBBIhudU3AH8TCxNguNx7xNracrhocd0="], "CachedAssets": {"J4EgphiFmdaPTgyASJJNd2agdgu/hb4dI91bEF4vJaM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-16T16:47:17.9299375+00:00"}, "ORtqfI1+XJDrndtL6ZqPKNVkyKae9TAxONc0ok5M5G4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "NGEWRrmKTLhMo5BFSTtoomjvOVVfFQqY/WOLFw38Nxc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "reiRraPhwWNvuDE0iQa++8j8mEe/W2aRtaWpXpePe8I=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T16:47:17.9347942+00:00"}, "qdFKpiFCos+XFXxbRKms+DDg0uIcoa/tMmoL7Z+tW5k=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "fuV+KoPOyRpbq65AyofkzWZxwD7eVuszqYrTco85EGc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "pS5C7Ef24FrMRjhqflu1eu4svNZvqjb7ANMh4TN61+U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "lZoFg5bWjFTmN4c4E3MijM+IDBKUqDASH0vJCSMupN8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T16:47:17.9381075+00:00"}, "ZWSBMFgKXTMUeYhdD5/JGJrkzKeOehIag9VQpU+c/z8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "mdhtb7hKBbRQCIv6np6SULapB3OPW7IAhr/7carAJQY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T16:47:17.9439494+00:00"}, "+tya5s5hIfTlnrZVf8OaN1C4mu/4iz7dCZu58PB8sGA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "Yl910uiI2bcs5vC5KQ6ApH3SwoqXvAqPZ9CT97NRvlM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "nCpCSGX8z591Zb3LdNqTeIpKl5Tud5VJEVhEMqJYTUo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T16:47:17.939107+00:00"}, "OhJK3zNcC6SoL9UXZU571CxRVh4qX1M1ELnP+zRjqBg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "TdU9CHtJKxw3XQWYhfWcszP8SCSi0OODlO71AdCOkZE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "mdPdYI8iR+nx9g/hVHbbIEdgvfLx3eS073nVLwdUjFQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T16:47:17.9439494+00:00"}, "RBW5k4g9VfG5U9W0p6gCVM3aS2dMK2sZw5go7qGsa9o=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T16:47:17.9469623+00:00"}, "Mz7kaGvTW4MbRUd37bwMnU2DnnAifynKvrmJ+KCSsTI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "GjEPGeGQLjR7t2qP8BXdg/kL7XvEcL3vYyhqaDkdxkc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T16:47:17.9449563+00:00"}, "91VCy8+QwQ6neSQEelfBVuU3tcFq6Mx61/WqeDmrGc4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T16:47:17.9469623+00:00"}, "SI5Bxzso5xP3NnMeQ3m40vz06OaZ4YmQSzbOxdjmtRk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "4KQOM6pSVy/XPm9+rCWdz6cDopO2RER/JpOUrXcOkFk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T16:47:17.9381075+00:00"}, "raJUNqVCpbuN5JuQJwxyK2lnTN1Amt6xqqYYhrMo4lU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "CMA2B8mXXT9YtqtXv6xJy9QmOMl9urzqQhaGstFX3BA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T16:47:17.9439494+00:00"}, "higtnSAg67URMorMXawg/rTYQHKTMPZshg5XwAue4x0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T16:47:17.9479619+00:00"}, "eMW0qpik7p8CdECggMc79TaOTbgtLPJfdmYlinT7t5M=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T16:47:17.9499621+00:00"}, "E9nidbXcpv3UDB642UAELu2neAoL4gef0fETXiNbxzg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T16:47:17.9523852+00:00"}, "b5Fe+bU1Wo13QI/SkGvTlnoMsuP+YiYrhPqN7rgjkHk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T16:47:17.9469623+00:00"}, "g696qRyV865HL9441B1/EiPzGWWjPOEs8sfp6T5Ci4o=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T16:47:17.9569039+00:00"}, "92V1wmS3Rk1ndjCFxtlIRxg3wLO8wbdKiavPTEFFADI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T16:47:17.9599047+00:00"}, "t2zl+tJ98T7mIaTOT2JKH73udHvywee9KjOB88g5JzA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T16:47:17.9548977+00:00"}, "vVzXBSWh4NUwhd42pPx9VvDopTBvvILFvmQZifzyXqM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T16:47:17.9509619+00:00"}, "oVhoFiIUNmWPREwKIA5QolrvwclsVovsylfWUFPjH+o=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T16:47:17.9620439+00:00"}, "ap/o5nbx3N5+UunisMTM4wadPYkdsnaJ6hklEtZcSL8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T16:47:17.964672+00:00"}, "FdJ8mhiK9O439bCDlxgQObkqEt51d2dc0Jkd+HcESd8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T16:47:17.9716771+00:00"}, "rUijvWNtvD1ai+UQJDx/2LdGMTaXZwu8VdejqAaNWeI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T16:47:17.9751866+00:00"}, "j5by2Pu79dLVrO2mL1n+v+I/vRpeYSern6JjyLQHRWE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T16:47:17.9835216+00:00"}, "fZLL+zhC/alhXHmmwjLcT2IZ+jYlmmNVhhYXG3+4veU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T16:47:17.9850413+00:00"}, "cMtnka2856U3dQyN0lxi2UcnXabXpBwy5qlnYmJS1FY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T16:47:17.9900362+00:00"}, "lKW9naUP2PAI/0NnECbZWRjR+NYFSRfbLi8AIyVFUxc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T16:47:17.9489621+00:00"}, "9HNV0k+jpOp8DZ11UXd99eDnZTCzbwPyiQhfmIDwUp4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T16:47:17.9381075+00:00"}, "AuTghBl9zv7lmn2PfpzDnGi4r+jPC+/iERUdSA5qKr0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T16:47:17.9401068+00:00"}, "Xk1K5VUql+ZDZSwdYgW1gzKWMAwZbTjOs+j7YD3fy34=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T16:47:17.9459625+00:00"}, "FL/nloeURiUVXIhuqk3NAVdDbtx6KlHMBPJ2q/WmsFs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T16:47:17.9499621+00:00"}, "ntXuV//78D7XMK8R5BVtdvKDwXKYyhA6N+lUUZjBsyA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T16:47:17.9579046+00:00"}, "e2YYlhrC389NZ22ZGpKXTRsW7cZ6YdtgRtlWJJUCGaA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T16:47:17.9599047+00:00"}, "Z2XjPWFp1qr060kSwKSzt2E+5Aded17nZJvNx4T+zEM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T16:47:17.963045+00:00"}, "Ho6DLdk0Tbva9c7WuuyQjhAO1iGD0o7bhAvbJxNCFA8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T16:47:17.9479619+00:00"}, "dEFlxkU8amGYCKddOncNWejqSMA9ZCxiRlPoOCqKJLs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T16:47:17.9489621+00:00"}, "Qtu8qOKigRTBLqf+aHWTh1fJwaiX4N7UFyUxLKnpClE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T16:47:17.9509619+00:00"}, "Wv4AfpI0POs3TmExGoq560C6xuCqP5ndauxSXa8yJW4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "YdbMRijkBNN0f5/mbp8aN5ivB8ifrCfxSbaIlPD8oqw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T16:47:17.9337868+00:00"}, "QGUDgzC+9R9AT9IQZTlk5Veg8bjAGt61mGjm1aSA9fw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "/juJmi/AK/hfqN7PpMt7pbutoApZhtxASOhHa+bKSTU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T16:47:17.939107+00:00"}, "Cwtd57mpI4thWgDoKMP7XAgMRlYGDhiuOidBpTM78GA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "xoEo/wKf82sEQtm3lTPj5kQddZcKS09WsEvTIjwXMcU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T16:47:17.9499621+00:00"}, "lEjBA7MYnFV5xirvVaCPCGkNoerdu6TQ6uicHkhbtrc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T16:47:17.9538918+00:00"}, "66s8RHucVzbeB/Yw5wWKWRfdUgh3WV4YXkcg5kfzJ6U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T16:47:17.9538918+00:00"}, "jiED3HSR6SoEuaIQAI5kAvuA0mNu63alTNO/lWrIwDg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T16:47:17.9599047+00:00"}, "oKP5COd2xi/xBOipj1LYUpByYJE8GEXkZgBIjJ0O2kk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T16:47:17.9620439+00:00"}, "51WnQGommA48/APA7ZQBTOAV4SqTVLFCMuCv1jXn9sI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "vJshXJD7C0i35Qq9AdkpCH+o0ua0lkOrti1yIGTeqY4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "8NaL4gUg8cSQYwQpioxk2uCTbem8jbL8QhzIn28Ywgw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lprs8bz5z8-sh82cr0ebb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "css/site#[.{fingerprint=sh82cr0ebb}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcujs1u80a", "Integrity": "lb+7JeK4Wv7wIEjWlJMKAfWf0K/1k6PU4NOWPrx+MVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "FileLength": 2353, "LastWriteTime": "2025-07-16T17:09:16.5898978+00:00"}, "yt79HlcCf3PencA/JwmAt+90trhLiA5/SArjQ2QegL4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yyut3rtmm3-61n19gt1b8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-16T16:47:17.9381075+00:00"}, "oYLerf1xMdWdI8nidZvkn6NnJ/N8GUSMcKzJmM5R5e0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d2s9mt8l3v-xtxxf3hu2r.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T16:47:17.9401068+00:00"}, "U38aAhajd3EeImmvuzJahN+WoyBPQVXadeI2d024eoU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o4jn2nrnp2-bqjiyaj88i.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "Gn64YMlFZBhrOI9BAcC3LznFhebSABFYpFKAv8fgihA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn20i0tc9m-c2jlpeoesf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T16:47:17.9449563+00:00"}, "+Zc9tdSHiev0/lD8m3KSdnkQS7oz6gnKMioDSAdODEI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xh28ms43oh-erw9l3u2r3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T16:47:17.9459625+00:00"}, "n0OV5C/KWLbIEW0mbMITy3MAZ1Q+GEG1gkGYKvR16Hw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bhug7or2m3-aexeepp0ev.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T16:47:17.9469623+00:00"}, "xFaELH9b5Zq9pEYXSsVQeu9YI2UvZcctz8FOmJzynA4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ri3kjc12fg-d7shbmvgxk.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T16:47:17.9479619+00:00"}, "r9UkTZhcW8yc7RHcMWzHaa1EYRozzEKdIty0Ug7iEJU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlb4e9rxe4-ausgxo2sd3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T16:47:17.9337868+00:00"}, "KjWpmEuhL/RYRuYZlzehoTPuyqfeHw9tdDPQukKXWUI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q8c3herb64-k8d9w2qqmf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T16:47:17.9347942+00:00"}, "q0yU+pDfuUCGWvqNdjoGGIndJsqlqMueW1rGHmcMEj0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\920u11wdzr-cosvhxvwiu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "7qQKhX2qQcNGR15NEQVWgQlTgy9DDyYrAvpt7J9nnUU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xo9ezg8h62-ub07r2b239.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "1zBfWoeAGm71Ek9DEJxb2WnGBNCsFZhE68Dewo4mbe4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\mhqzf0e8x2-fvhpjtyr6v.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T16:47:17.9401068+00:00"}, "DVw/DH34bHy9B4UA4TVxL/FG2mwQAjsMDJ8lczpNWxs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0j4di07hif-b7pk76d08c.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "H8QA+Z54LkRtxBkwZFakuZZY0EEM2iviVyZYSL/8bDI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bk6g14ginu-fsbi9cje9m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "SxWqRU/QXUTKTH/QRk3ZJcFPTFb6hqj9472T+gL4pZE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q6yglsnvlc-rzd6atqjts.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T16:47:17.9439494+00:00"}, "oeqLRQEVRYWVy2RZcKVSULzHdoM61Kr0WHrkb5zRXXQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aqwvpajyd3-ee0r1s7dh0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T16:47:17.9479619+00:00"}, "1NcaEbGtbKalOk+8x4Q1X9hB/qkRnuNv/mNwHHaB2vU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tlhotw0jnm-dxx9fxp4il.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T16:47:17.9499621+00:00"}, "5ujbyOor05nohMItyQhYnyp1/FajCNuc5NsKmkGBSY4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\kkurdxpm9f-jd9uben2k1.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "dcqojcGnWKTE/zH2gV3JPRwGvYiwv3wPShUQeRvb3Ms=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yhfvs28ewf-khv3u5hwcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T16:47:17.9347942+00:00"}, "GZ7pLtYCAM7JpVS5PJS+xPEtLH5l4djenxuwAnXaJd4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\f1ac5fdwia-r4e9w2rdcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T16:47:17.939107+00:00"}, "guNTrvxvG8xYtPGcNfc3q0soPGtJsvxSZdWx0ns8kyQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jhq81lqatb-lcd1t2u6c8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "cECPDYVtsxvOipLnswpCiXeDA2mcMA0V+NF3mSvM/KQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oiqa6mlryd-c2oey78nd0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "SgFD60aBYjNyG9XX4lWrG0p4JDeBWXlQf9Iolp2IliI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5c5phhd1cg-tdbxkamptv.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T16:47:17.9449563+00:00"}, "VE82T6+d5dRBPbVsY5AFX4c/ir/flclzjeibjwSIvyM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9tv9vhb97z-j5mq2jizvt.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T16:47:17.9489621+00:00"}, "53Zhy18xeNPPennxHIM1en0tNoPTokWRhptIeN6zUnI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\dsz1cg7n1f-06098lyss8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T16:47:17.9489621+00:00"}, "7mLiOU3hxA5gcI+pZH2+xVEL+FWgJZglaMc1uV+vNu0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\80l2hrri5r-nvvlpmu67g.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T16:47:17.9523852+00:00"}, "JxBDH4pT9pXWm+JZg6wi1gjJL0LdmgrvArrR3w57hzw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hurbbjqq0l-s35ty4nyc5.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T16:47:17.9559039+00:00"}, "E8siw4CU/4Fwtg69BwqunoQZlQm520lPj2sPwLdJS08=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nocwlel4l8-pj5nd1wqec.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "GjMnqIOgpWn2/tPxAoHl47hZnd6YUQOSqCr7H18DvwM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2o1knfnnfx-46ein0sx1k.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T16:47:17.9449563+00:00"}, "7kRn4wH2zLTTLm9Bz97pfsm6szpBPX1xZZrXKn5O73A=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s2f91uc2id-v0zj4ognzu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T16:47:17.9538918+00:00"}, "lFe9j5J1uC4tCvBsN9944auPpv7kEA76Y/Esn8e4Zn0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6t5pwr2opk-37tfw0ft22.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T16:47:17.9538918+00:00"}, "hYkUQRJwyefzhwz5sLoJnKb7Vi4sZvQvpsd94FW8kg4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zjnmpfyr4b-hrwsygsryq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T16:47:17.9620439+00:00"}, "2GWjAWB+s+m/jZEMcC2KLIpxeXW3mDiOfRyPpLCy6yE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d9lo8u5fbp-pk9g2wxc8p.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T16:47:17.964672+00:00"}, "7+Qz5G7p9D09nMWyBPu7mNn8RHPr+namR7Yy8K9KkOk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rd59fx66rg-ft3s53vfgj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T16:47:17.9716771+00:00"}, "eF7So+W2/Q/Ycjf0WFv3AaJj7oQTuw1F5NqAbCGKvoo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1kvxxii4kz-6cfz1n2cew.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T16:47:17.9741824+00:00"}, "wpJodjN7xAoYs2mZ0Qoj3NiUKu7HIwSu2lxNOXwi6Tg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlj8cqdux7-6pdc2jztkx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T16:47:17.9821976+00:00"}, "oyX38JQvq8j+ahD+5553XOA0i1dOLyUW+3nVkifSTCM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\y5kr9kwq5m-493y06b0oq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T16:47:17.9840247+00:00"}, "n8Lz8gDVcT0FXolJuL5LRX/VtWiGomXS1AjQt6jsj/U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\w8uh90b10i-iovd86k7lj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T16:47:17.939107+00:00"}, "ss/3KW26Ol/b+ypWeMn4G65CVNCx81A0WPZ97shANZk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2ruhixa8xd-vr1egmr9el.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "vgra6yf+KuU/84fqGxjyCf6yq9Pc8inrJY9knu9FVH8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\foy5i872gl-kbrnm935zg.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T16:47:17.9489621+00:00"}, "MHdyxCTLtu5R62RRxCCBV4MVgQjYSat+LFAAaU0Pj9Y=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2z1qdpnfqu-jj8uyg4cgr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T16:47:17.9509619+00:00"}, "V1wkXkLNZ7ckqT0T15uEsEdvSPOExy0a/Jl90epNy98=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\muzxgjo37n-y7v9cxd14o.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T16:47:17.9569039+00:00"}, "lzuUr9Y3Snzwg2Rw5Xj80Wtv5DTRK52ove9PEoyjfUU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lpjzqdhimc-notf2xhcfb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T16:47:17.9599047+00:00"}, "e3qUoNwHlAnvXksoBvG7DDHSHeJuuTrN/KYvOympmQM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z8ri5bwbu2-h1s4sie4z3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T16:47:17.964672+00:00"}, "px8U/MKWbZocPV2ErtfLuBzFoKaTHiDAvZosk3LuboY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\fhw1z6t0qj-63fj8s7r0e.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T16:47:17.9509619+00:00"}, "c1v4tW7s3Ls7Nn5LusKiNgd8uMPDbw91OqtDZJRzAbo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\stamlplyst-0j3bgjxly4.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T16:47:17.9548977+00:00"}, "xzGEzMbkqyoM8fFB3lVLYp4FLw8M9eCPzDLgvISFBKs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5ub76y8isd-47otxtyo56.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T16:47:17.9559039+00:00"}, "WabUfCNNs8CGmtBM/XCHIEaVNii9gNf/dxx4S5qIzF4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uz19c4uzko-4v8eqarkd7.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T16:47:17.9322813+00:00"}, "S4PiSIv9rZBNKR4cYCZCxXpgydg8vz8knLlH1nlv3gs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3eodnqfdjc-356vix0kms.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T16:47:17.9337868+00:00"}, "9Zerb5zmjJD0XIVL06O1eqcm8T5iKNTEJqXHCWTV07A=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jh9e11aaux-83jwlth58m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "EGCpCfA/0TzJ5mIOKhSu5ADzMBukBGRERCACM3b0kVE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\m0lf1s7rkh-mrlpezrjn3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "lL+JilXl2Z1L7rGx/jvPOKhc9hnAnYuyWv30/TrZan4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6j2252c6ih-lzl9nlhx6b.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T16:47:17.939107+00:00"}, "LMqel3tdbtTgS6vv4MWuZX997eMhu8wHkyaN8RdAoMc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\za5cs44o7u-ag7o75518u.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "pJ1J0RBQkGm1rL6W/QtJ5LSWMzxdmuNcLu+M7tB2t9o=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5u6nkyp10k-x0q3zqp4vz.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "jaJ+ddWzJmmWhqU/JflsOoZQBtGDJC7ODCvCeipV0Ko=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yw253t2yss-0i3buxo5is.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T16:47:17.9559039+00:00"}, "/4Fz6U5j/nLaHMsselA9UEszoVNCzwLkzRoq/7HFr2I=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6id4mbo8qd-o1o13a6vjx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T16:47:17.958904+00:00"}, "uG34zj+bQY6Wv5vtFAB3ozt9ByGoj1fy6yiN3iHnyFQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ajf14tfrko-ttgo8qnofa.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T16:47:17.9620439+00:00"}, "WMt7akRTndZFf4a+bS0SS34SiUcusXFBnv6xionEYrQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn8985znn9-2z0ns9nrw6.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T16:47:17.9358001+00:00"}, "ija2pS/JFOoQCB/VfKSILFkdFYVu7M2/Wpe94pcujUE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aeg74zr7ou-muycvpuwrr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T16:47:17.9371+00:00"}, "6GEFEy4XWH3cAd1pALrW5xTrVAIWAHHsrKRA0rIiY00=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l9n6f8vp69-87fc7y1x7t.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T16:47:17.9411817+00:00"}, "Lh9Q9/fsldhG4lySwdrv23hp17GxVPm+SOLMMx7ahG8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rot3sofme1-mlv21k5csn.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "N1igSzTCSoQVC3c5TS8A6HNYhg7Nrw1D9xJ8z4V8SBw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5rw2t3q994-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}, "+NcSVD6w9ZjaBBIhudU3AH8TCxNguNx7xNracrhocd0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4tw343vpw4-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-16T16:47:17.9424414+00:00"}}, "CachedCopyCandidates": {}}