{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["1cbOjJog5UxMisVWH3vqt5FAwi2zu3VZP+JHY54uS3Y=", "Aw7UufjQ7OQNHgh2RfDhmZpiu4HX+zh1HWCe1n9a+fk=", "6VsHByjncwIB16il98bFKFVuuRANkJu32lRfBhvPGd8=", "uq6+GPgs1pehiXOC61du65HYAispQyT9SuQx4G+lX0c=", "s7R9hXWUmvI75Eax5uDnFDhP2JaYRCC0StVAD6Erw10=", "8Swz1Lt6a6H8pqGcprsAvltyuns190vB9JADB36ObIE=", "b4JUa9z6pFltL1FuvzwKc1oOma2DAMQvgBJEF8JEcFY=", "Mdm/l0kVv+e305aYSjaeE1FjOFliKNq2jQ5OQ7sdffo=", "sd3pgTibTwTCXiQEshdXC1i07S+k+Yw86fExd3WfNzY=", "QaHSwgW+gbJGr/8/rnOGU/N2IPy8zrOezr5qlo1skNg=", "ZCYkNU4/H58DI+yAeG/tD15Ht1nOAALbkQZtC55SWD4=", "zmiiXi2XKsM+l/ViWzq1UVJBMd37+R1YC7CBXJj6K2c=", "6Ubyi3VlJfg1M7LUaSf1ERS1RPe8lP7MJjBT/5Rf2Pw=", "hz13F0ZxLcp+P1VPJX8qCOGlIvGutSYlS5lEt1VlibA=", "tIjBLKxGn+d4WhmJYBQek1qfJ8Z4TE6Z76ZApcSLhxc=", "vPIq7er6UogqDesqH9DGWd942+mGk4baCReAYSFLv34=", "EaufjAjjVWEVDMS+ajitze2hGKLwUJiezy6e40s0mqA=", "Nygvm97iyQtpyPV/BR/Ufd9K3C1krbDwnsBDZOPa6fw=", "O/Vp7c2/Q0jh2Kqv/0biSKryS5KLjFRf87/QvP3uhuY=", "b0iK5BoogNEDS7HckbWxNZLmzSgA/uBHFv2AJrD2G3g=", "2XAzkYCcvedoJyW6Htbbios68B99AZwhhegyFViai4g=", "O5txG+3RSXi/6vtFyNniwhStgYj/sDVtSedm410SwQs=", "IkexHY4wVtSB3NQMVDsdaVHKrMSM7rMBEcPd9PN7lzQ=", "WtKWcN+v0yLCHNNlbeAzW3nBra2t/EPx5T2eXs9Mnlg=", "GMETogru1Q86pZA+DUYc5da494akIorucSyKEN8V/Cg=", "znkW9Ha2Nqm6S1k3CX5O3QrxyLiDlG6ZoOQE65rIxnQ=", "pccK417rt+BD8dBsSFAtK11wKKSm2mX9GmG71gKJO7c=", "thQJ+orEs2DV2hTP+xEACX3soXhW6jeI+EGfGF2J0PE=", "lLQ8fLoVba8DBmRflF8xHPnPb0UInzeeqviuaEp3NYg=", "psOBNTs0nLvIhK045G1i10WEV0E0n/tsRmr5wNtDTQU=", "l0K6jkBF8K1C8E3Z6cPnjqTULVo3OTeUjHsAYepLakQ=", "lphLTHVtwk5knmKCS/XyRHHrg8EChJVQK31yktBwPN8=", "7XTrQnQSdcD1cF7yPhjl7NpUcIO+L8HGm5dP+qquq88=", "NM/tGgdFr3QjK4Dgmc76DKwMW4g047d+QJGFCbrFzC8=", "peItBIqGjajBxtLBOBulLARiv2tZ9rS+s5wk4bN0JFQ=", "tMIxXr3qx7zbTStSx1u8D0WePkeB6Mam8wUnEZeUPaA=", "UjHopzCMGrXZhacbhB3q2uoVUTa+mipVjx8OEU9D+Vo=", "rPn7x7hoIan3OgI7Kpeu5SwsaViLywxh8OdfRuf6NHE=", "Q6ScFmx4UDohABHAJDGW3crrT7WL19Y7Ejq2MbfVV28=", "oTKd1WV3dYnoQWCK2Gjoq79mOQrq39YihADCkP29mk8=", "OQjGFqp5VOgF97zWFVMLuDnt1/NgfA3fkHAwXDAS7L8=", "3zE8XnlzITnynJmWZb1oVE6TIIABxHIenLNBSlFISds=", "pWkCcqoKrgN4umT3VGWT7sOE1P3DVJtt/0I1vHZWzuQ=", "Lp+fQh+Go8xj6qSk7o/oSch092I1HEhPCfLA1S6FL+4=", "J88zYRrVmW8dE/N4I5pCfjEtRnQItIxoCmC+n1pYtc0=", "TQdAtX0zpXsMVHD45Q0ppwY1iRXGv+xjfHfW+Z/ypWs=", "D3zmSjh9FlFWcV8VH8x4z1lnRjVSxtwL9vXzjmWPGaw=", "Yl/nEablzlRGs16Vg9Bg+3KjC+uJk0zf24pQaYa8hEY=", "K8C9Zi1arn+sWPS/kyjKH1aenC8CyasARE/gwcqjyxA=", "8rtOcc9uP+UgNOMFgZQPFU/tlUaNbwDIyuvIJ8WFyg8=", "GBkm4No9eQZ2g7xhRlqURgCxn5tDdVJcWbjR5FSboRU=", "iWx0KPY+KlqvD0kjX90TWlvPMMZWWnhe10eO334gO/8=", "GlgSjYdrLE90G0/zVl5q3bnGPc5ndOr+e9L4rXzuAlk=", "8tj5Nx7sq18UfHnIUvxpE2TD+z9JMRz+kKya5X7Drtw=", "RKFrrYchS+rOjHu7LrfZ8KsXNVS+QB1GoGfzq0ZpBK0=", "6xk9EEG7nG/di8B2faXn7hr15dENBj2vB4CqyG4ZZQU=", "GklWBcOiR6kf2tKqMt9gPdkAt0GWFmcw9r38VRJOlxk=", "FNByx3jTUH+l2pbGSrXtybSdeqDMz2VRdnVcQ+4S9HE=", "D2nQ+xOj1A0LOa4Fm7g24xsRRDYE3pgPt+G+l6DWD58=", "3poC9c2wcAK8kEOkkyloSGEIPK1QpP0/H4qHBCAZ15Q=", "HrHdyzgr2dz2xvFdHV6RYxTWisxLLkgyjWSAqBuIqBw=", "NQ3leDudMw3skwnOD4hG6uFKSqyJckSlEW26moDNdeo=", "OHRKrn3Kvrrb7uJGl1gAH3be7xbuL+sqFSwBdocj4ro=", "E1EoG2kOVADCZZ5pFNReXNJUqHpX87dhZo58JeAlPwU=", "8SVWp2rOlScsPKUKFU62eo4NA1apAv62mI1XH8P3uT4=", "XeXkwYxHTCe3uxkZO+RkwUr++ucte3zItSRSlyToR90=", "zN9fsJBEtDjRQiPMDAII8zubfL5bc/kQZkRJ2UPZpp4=", "/STsZINRPewUcDjD6Fis+r+LGT9p5BmqU/IPlXg3/Qk=", "TN/gHwoYSvVwijHmmo2SLMu7fLDpwM19iJ5XYUR2N2E=", "tlH397KDaf0eUkNM09JLBlqX5+j8GAdbyUSRsnA8HYc=", "YFOObedlFnptavVoa6pbiFK1acSTtvB5wkErKaKZCHc=", "iwgN9TN0BY2Bkh4DW8M9JUkRVIIKAZGJ/aKe1Cua3RM=", "2dbZ7ibWL6G34nGUtHxVCxQWEcx1+nirz6MzRSEu+hg=", "s/1MnWIFDF9BJGcwKRc8xB0jkQNPlH4sMYoa/TkSj3w=", "GfYWv5tmOJHyWLk2BbpxdObk2kb+k9YB/FYlCxufV20=", "9Jw5ZBU2y7OCsB8UzaC/jVzJlCfmgK0WxCrwkZnKlV8=", "kismTxajQyE12LjXisjfLQcNUGeHwMrPsd0elqgPX/g=", "Q4AQkfWU1EKgRHas+qTcbFPR1v2hZxxFWvOQvxPOMAM=", "FZ8SFmEhcSPo6WoGLQy0UaVRjm2te6AupGMCvyqY58Y=", "NIYNb8r+04WOA2vykh0SBii2Vcvc9tXNnSCLSF4FskQ=", "PcqMMQZpO/D7UcLh51sUMrc9wD7LCjc6rvT4Eph2BVs=", "NVY8CvZKitkkdO6YxaHF44WQpKgTIueYuYKLX7RSGfE=", "3imUVs+QodCtLTpVdgvqWG7EaOa19r/UwKSI+Z+Qzok=", "4jPv7ZDFc4UdJ+ZaAIsf5gco0zlSazctXlyisjb4KSA=", "PDA6wMF6XNpdn2+q6j8PDiC0qYMdRpRhkF4ynQgf8AE=", "Fs8fn5JNkvIDyrOSgXzdbTd3CCE6txrPLp0bJQNMNyg=", "qi4i/cniXlpTPHjzXvhFYK/DcEH+AC75CXsM1o/rNVU=", "S+vat0h+5LSqtzbZsEyUeZ3tSFz9hmsAQGl1XkuPTOM=", "VDbZco+aT2SQEj2TD8l+atoxkztgufsZ6WdsM+5yxRA=", "TJ6FvxDmSvKn4D7CKrAJOWygFl8hSAZgBAGrjIOZK98=", "QuY+9hxUuFsovv38l0caHeALXY77jZfupqzEiICkrsw=", "UCI++w5wOo1EYbw3V4BsNHvLVy/Ewy5QlpAez8Iq1kU=", "n1ijt+5ngwn4MYKChv02P5Qi+ranpyQgEJ/aiEmByiE=", "s2Y6Yy4XI2DDLB4rFwFv4amos+LKEAAtfIEgCxaUWNc=", "3Ku9H0pu1wPDbPXVd2D2sxdazkw9KEGKmdnn0/mujDI=", "P7BhulyIqv7HSnEPkCoOQkDtYvuut7uSQZrIIiXz3mU=", "F/2+LvOz8YuJZSSlu2X5CPulyEAs9QzP6DxBGF9gLZg=", "v5mIuo460+R7idT6FkuL/NlKBmfgT6qNFGR9ks82Oas=", "E3Mk6MmpxO3GoTaavNHQ22yhw86qOqf44GGTN6LtY2E=", "FbUmqa+rXyQ8QIAS7DErvgLY+CcKZvOZ0AdDSl7+COI=", "PoodXtssl0YY+j/RaZ4h9E46WJkgtC+O1r6XSZPAkhA=", "J5jigu1vXTH6EvXblS/VyHg5IgV+xbIZxijRu1saNC0=", "yolL5BvOB4pG2wXu5xJtSWMdnGFUEpXJ/aHbf7W96Is=", "7mnpBdkZU1m3O8SKaRZIMLfaIXJEFjhEpqAn0Y4uiy0=", "VRwgK/4M8TqwQa9LgoIT/tkfXEa5Wo/C/Y7WrNuduJo=", "o6abM1M2X5fPqDwSktLRBL855Wz5WCK3sbFYJ2LRhqg=", "/FvkDutOcudqw19s7CCY9p+t1IlXBOphKJpnp54EUO0=", "kCkrG4HUA1GrbQBl4sdYTnC7CTAiGohVPdl4s52Q1NQ=", "eIfchr4UoU21oz3Uf2XCURPzIdfU46kJwmhF9CV95QQ=", "SNsnffKbLsZG0rJpHVYxP/tqnRvTs4sMMqEoIyZi69M=", "BRdGVaWCTPBuIH0jr1xxnbANFHpgnEvLqq6wG596BbA=", "oNHMsD8Qdo7ngVoFmKMBJqkbUWfLhVwboz0vsUBypH0=", "D72MVhByAw6Nh+sGdK6DcgGJbPr0cMiB5eaq0XHNPMA=", "25eqJY2PDOLqZ3anwP/cf4mUnqFApEmczit3wrWLKpM=", "WGhql7PsQjniIpjNJ0j2UFV8tK1V015wrCszgejxBHE=", "QkDJvcXfB1PQgzI8YS7W0aM4FXCy4SgYj4u3Kde9KvQ=", "a0vxXPuQ/UkcNP6gd+dQhx/rEe7wDv204D9MhFHEMWU=", "xXDX3wMXhh7XseIJr54k3g25U7sA2eMvuqKXVQfagak=", "Vp9aQTTfd6s0NyIMzfu5HH2K+zU/KS5ICzYxG7F13rw=", "jzlhqJi/p48Dyur9UmDSdAoZPI/uOXJ7769O8LjcMM0=", "fsXsCgCas0c9TdZDS0eHi3NSPdEuLQ5Cd3sHvspaxuk=", "R2xR3t1ltX7PNjmCdKKb5gvyYUagjwj6boHX1PdVjcg=", "cSxVva5lJIcMQUhzSPw1BBj2aYOfDY1v0LQvfz1LxT0=", "ekaoXiJ5lXUSq/sNQ/IDOAzUOhzvp8ZjqmtYfDU79y8=", "uch805kBTKrZI/jq5EGGvFujfbrovxNMCJOi8sq79CU=", "jEavFXeuD4eE7uWiXO1dRXMNZyDzuXTJWQOE0WTycW4="], "CachedAssets": {"jEavFXeuD4eE7uWiXO1dRXMNZyDzuXTJWQOE0WTycW4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4tw343vpw4-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-16T18:36:55.1563142+00:00"}, "uch805kBTKrZI/jq5EGGvFujfbrovxNMCJOi8sq79CU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5rw2t3q994-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-16T18:36:55.1487794+00:00"}, "ekaoXiJ5lXUSq/sNQ/IDOAzUOhzvp8ZjqmtYfDU79y8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rot3sofme1-mlv21k5csn.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T18:36:55.1457799+00:00"}, "cSxVva5lJIcMQUhzSPw1BBj2aYOfDY1v0LQvfz1LxT0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l9n6f8vp69-87fc7y1x7t.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T18:36:55.1457799+00:00"}, "R2xR3t1ltX7PNjmCdKKb5gvyYUagjwj6boHX1PdVjcg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aeg74zr7ou-muycvpuwrr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T18:36:55.1409331+00:00"}, "fsXsCgCas0c9TdZDS0eHi3NSPdEuLQ5Cd3sHvspaxuk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn8985znn9-2z0ns9nrw6.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T18:36:55.1387029+00:00"}, "jzlhqJi/p48Dyur9UmDSdAoZPI/uOXJ7769O8LjcMM0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ajf14tfrko-ttgo8qnofa.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T18:36:55.2387704+00:00"}, "Vp9aQTTfd6s0NyIMzfu5HH2K+zU/KS5ICzYxG7F13rw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6id4mbo8qd-o1o13a6vjx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T18:36:55.2355741+00:00"}, "xXDX3wMXhh7XseIJr54k3g25U7sA2eMvuqKXVQfagak=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yw253t2yss-0i3buxo5is.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T18:36:55.2310503+00:00"}, "a0vxXPuQ/UkcNP6gd+dQhx/rEe7wDv204D9MhFHEMWU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5u6nkyp10k-x0q3zqp4vz.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T18:36:55.2229803+00:00"}, "QkDJvcXfB1PQgzI8YS7W0aM4FXCy4SgYj4u3Kde9KvQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\za5cs44o7u-ag7o75518u.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T18:36:55.2134526+00:00"}, "WGhql7PsQjniIpjNJ0j2UFV8tK1V015wrCszgejxBHE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6j2252c6ih-lzl9nlhx6b.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T18:36:55.1553127+00:00"}, "25eqJY2PDOLqZ3anwP/cf4mUnqFApEmczit3wrWLKpM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\m0lf1s7rkh-mrlpezrjn3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T18:36:55.1467806+00:00"}, "D72MVhByAw6Nh+sGdK6DcgGJbPr0cMiB5eaq0XHNPMA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jh9e11aaux-83jwlth58m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T18:36:55.1387029+00:00"}, "oNHMsD8Qdo7ngVoFmKMBJqkbUWfLhVwboz0vsUBypH0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3eodnqfdjc-356vix0kms.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T18:36:55.135485+00:00"}, "BRdGVaWCTPBuIH0jr1xxnbANFHpgnEvLqq6wG596BbA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uz19c4uzko-4v8eqarkd7.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T18:36:55.1340562+00:00"}, "SNsnffKbLsZG0rJpHVYxP/tqnRvTs4sMMqEoIyZi69M=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5ub76y8isd-47otxtyo56.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T18:36:55.2279946+00:00"}, "eIfchr4UoU21oz3Uf2XCURPzIdfU46kJwmhF9CV95QQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\stamlplyst-0j3bgjxly4.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T18:36:55.2279946+00:00"}, "kCkrG4HUA1GrbQBl4sdYTnC7CTAiGohVPdl4s52Q1NQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\fhw1z6t0qj-63fj8s7r0e.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T18:36:55.2229803+00:00"}, "/FvkDutOcudqw19s7CCY9p+t1IlXBOphKJpnp54EUO0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z8ri5bwbu2-h1s4sie4z3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T18:36:55.2144603+00:00"}, "o6abM1M2X5fPqDwSktLRBL855Wz5WCK3sbFYJ2LRhqg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lpjzqdhimc-notf2xhcfb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T18:36:55.1729122+00:00"}, "VRwgK/4M8TqwQa9LgoIT/tkfXEa5Wo/C/Y7WrNuduJo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\muzxgjo37n-y7v9cxd14o.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T18:36:55.1608052+00:00"}, "7mnpBdkZU1m3O8SKaRZIMLfaIXJEFjhEpqAn0Y4uiy0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2z1qdpnfqu-jj8uyg4cgr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T18:36:55.1563142+00:00"}, "yolL5BvOB4pG2wXu5xJtSWMdnGFUEpXJ/aHbf7W96Is=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\foy5i872gl-kbrnm935zg.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T18:36:55.153291+00:00"}, "J5jigu1vXTH6EvXblS/VyHg5IgV+xbIZxijRu1saNC0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2ruhixa8xd-vr1egmr9el.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T18:36:55.1457799+00:00"}, "PoodXtssl0YY+j/RaZ4h9E46WJkgtC+O1r6XSZPAkhA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\w8uh90b10i-iovd86k7lj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T18:36:55.1397102+00:00"}, "FbUmqa+rXyQ8QIAS7DErvgLY+CcKZvOZ0AdDSl7+COI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\y5kr9kwq5m-493y06b0oq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T18:36:55.3268611+00:00"}, "E3Mk6MmpxO3GoTaavNHQ22yhw86qOqf44GGTN6LtY2E=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlj8cqdux7-6pdc2jztkx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T18:36:55.3228551+00:00"}, "v5mIuo460+R7idT6FkuL/NlKBmfgT6qNFGR9ks82Oas=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1kvxxii4kz-6cfz1n2cew.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T18:36:55.2789673+00:00"}, "F/2+LvOz8YuJZSSlu2X5CPulyEAs9QzP6DxBGF9gLZg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rd59fx66rg-ft3s53vfgj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T18:36:55.2654303+00:00"}, "P7BhulyIqv7HSnEPkCoOQkDtYvuut7uSQZrIIiXz3mU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d9lo8u5fbp-pk9g2wxc8p.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T18:36:55.2527721+00:00"}, "3Ku9H0pu1wPDbPXVd2D2sxdazkw9KEGKmdnn0/mujDI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zjnmpfyr4b-hrwsygsryq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T18:36:55.247256+00:00"}, "s2Y6Yy4XI2DDLB4rFwFv4amos+LKEAAtfIEgCxaUWNc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6t5pwr2opk-37tfw0ft22.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T18:36:55.2377611+00:00"}, "n1ijt+5ngwn4MYKChv02P5Qi+ranpyQgEJ/aiEmByiE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s2f91uc2id-v0zj4ognzu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T18:36:55.2749572+00:00"}, "UCI++w5wOo1EYbw3V4BsNHvLVy/Ewy5QlpAez8Iq1kU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2o1knfnnfx-46ein0sx1k.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T18:36:55.1553127+00:00"}, "QuY+9hxUuFsovv38l0caHeALXY77jZfupqzEiICkrsw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nocwlel4l8-pj5nd1wqec.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T18:36:55.144772+00:00"}, "TJ6FvxDmSvKn4D7CKrAJOWygFl8hSAZgBAGrjIOZK98=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hurbbjqq0l-s35ty4nyc5.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T18:36:55.2330484+00:00"}, "VDbZco+aT2SQEj2TD8l+atoxkztgufsZ6WdsM+5yxRA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\80l2hrri5r-nvvlpmu67g.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T18:36:55.1789825+00:00"}, "S+vat0h+5LSqtzbZsEyUeZ3tSFz9hmsAQGl1XkuPTOM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\dsz1cg7n1f-06098lyss8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T18:36:55.1714024+00:00"}, "qi4i/cniXlpTPHjzXvhFYK/DcEH+AC75CXsM1o/rNVU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9tv9vhb97z-j5mq2jizvt.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T18:36:55.1597968+00:00"}, "Fs8fn5JNkvIDyrOSgXzdbTd3CCE6txrPLp0bJQNMNyg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5c5phhd1cg-tdbxkamptv.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T18:36:55.1553127+00:00"}, "PDA6wMF6XNpdn2+q6j8PDiC0qYMdRpRhkF4ynQgf8AE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oiqa6mlryd-c2oey78nd0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T18:36:55.153291+00:00"}, "4jPv7ZDFc4UdJ+ZaAIsf5gco0zlSazctXlyisjb4KSA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jhq81lqatb-lcd1t2u6c8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T18:36:55.1467806+00:00"}, "3imUVs+QodCtLTpVdgvqWG7EaOa19r/UwKSI+Z+Qzok=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\f1ac5fdwia-r4e9w2rdcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T18:36:55.144772+00:00"}, "NVY8CvZKitkkdO6YxaHF44WQpKgTIueYuYKLX7RSGfE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yhfvs28ewf-khv3u5hwcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T18:36:55.1364916+00:00"}, "PcqMMQZpO/D7UcLh51sUMrc9wD7LCjc6rvT4Eph2BVs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\kkurdxpm9f-jd9uben2k1.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T18:36:55.1340562+00:00"}, "NIYNb8r+04WOA2vykh0SBii2Vcvc9tXNnSCLSF4FskQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tlhotw0jnm-dxx9fxp4il.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T18:36:55.2330484+00:00"}, "FZ8SFmEhcSPo6WoGLQy0UaVRjm2te6AupGMCvyqY58Y=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aqwvpajyd3-ee0r1s7dh0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T18:36:55.2320497+00:00"}, "Q4AQkfWU1EKgRHas+qTcbFPR1v2hZxxFWvOQvxPOMAM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q6yglsnvlc-rzd6atqjts.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T18:36:55.2290398+00:00"}, "kismTxajQyE12LjXisjfLQcNUGeHwMrPsd0elqgPX/g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bk6g14ginu-fsbi9cje9m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T18:36:55.2279946+00:00"}, "9Jw5ZBU2y7OCsB8UzaC/jVzJlCfmgK0WxCrwkZnKlV8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0j4di07hif-b7pk76d08c.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T18:36:55.2269947+00:00"}, "GfYWv5tmOJHyWLk2BbpxdObk2kb+k9YB/FYlCxufV20=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\mhqzf0e8x2-fvhpjtyr6v.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T18:36:55.2229803+00:00"}, "s/1MnWIFDF9BJGcwKRc8xB0jkQNPlH4sMYoa/TkSj3w=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xo9ezg8h62-ub07r2b239.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T18:36:55.2134526+00:00"}, "2dbZ7ibWL6G34nGUtHxVCxQWEcx1+nirz6MzRSEu+hg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\920u11wdzr-cosvhxvwiu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T18:36:55.1759727+00:00"}, "iwgN9TN0BY2Bkh4DW8M9JUkRVIIKAZGJ/aKe1Cua3RM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q8c3herb64-k8d9w2qqmf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T18:36:55.1573133+00:00"}, "YFOObedlFnptavVoa6pbiFK1acSTtvB5wkErKaKZCHc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlb4e9rxe4-ausgxo2sd3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T18:36:55.1387029+00:00"}, "tlH397KDaf0eUkNM09JLBlqX5+j8GAdbyUSRsnA8HYc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ri3kjc12fg-d7shbmvgxk.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T18:36:55.2427862+00:00"}, "TN/gHwoYSvVwijHmmo2SLMu7fLDpwM19iJ5XYUR2N2E=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bhug7or2m3-aexeepp0ev.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T18:36:55.2412756+00:00"}, "/STsZINRPewUcDjD6Fis+r+LGT9p5BmqU/IPlXg3/Qk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xh28ms43oh-erw9l3u2r3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T18:36:55.2407696+00:00"}, "zN9fsJBEtDjRQiPMDAII8zubfL5bc/kQZkRJ2UPZpp4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn20i0tc9m-c2jlpeoesf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T18:36:55.2512657+00:00"}, "XeXkwYxHTCe3uxkZO+RkwUr++ucte3zItSRSlyToR90=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o4jn2nrnp2-bqjiyaj88i.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T18:36:55.247256+00:00"}, "8SVWp2rOlScsPKUKFU62eo4NA1apAv62mI1XH8P3uT4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d2s9mt8l3v-xtxxf3hu2r.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T18:36:55.1597968+00:00"}, "6VsHByjncwIB16il98bFKFVuuRANkJu32lRfBhvPGd8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T18:36:55.144772+00:00"}, "E1EoG2kOVADCZZ5pFNReXNJUqHpX87dhZo58JeAlPwU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yyut3rtmm3-61n19gt1b8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-16T18:36:55.1477791+00:00"}, "OHRKrn3Kvrrb7uJGl1gAH3be7xbuL+sqFSwBdocj4ro=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lprs8bz5z8-sh82cr0ebb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "css/site#[.{fingerprint=sh82cr0ebb}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcujs1u80a", "Integrity": "lb+7JeK4Wv7wIEjWlJMKAfWf0K/1k6PU4NOWPrx+MVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "FileLength": 2353, "LastWriteTime": "2025-07-16T18:36:55.1457799+00:00"}, "NQ3leDudMw3skwnOD4hG6uFKSqyJckSlEW26moDNdeo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T18:36:55.1457799+00:00"}, "HrHdyzgr2dz2xvFdHV6RYxTWisxLLkgyjWSAqBuIqBw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T18:36:55.1387029+00:00"}, "3poC9c2wcAK8kEOkkyloSGEIPK1QpP0/H4qHBCAZ15Q=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T18:36:55.2587886+00:00"}, "D2nQ+xOj1A0LOa4Fm7g24xsRRDYE3pgPt+G+l6DWD58=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T18:36:55.2527721+00:00"}, "FNByx3jTUH+l2pbGSrXtybSdeqDMz2VRdnVcQ+4S9HE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T18:36:55.2377611+00:00"}, "GklWBcOiR6kf2tKqMt9gPdkAt0GWFmcw9r38VRJOlxk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T18:36:55.239769+00:00"}, "6xk9EEG7nG/di8B2faXn7hr15dENBj2vB4CqyG4ZZQU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T18:36:55.2365737+00:00"}, "RKFrrYchS+rOjHu7LrfZ8KsXNVS+QB1GoGfzq0ZpBK0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T18:36:55.2279946+00:00"}, "8tj5Nx7sq18UfHnIUvxpE2TD+z9JMRz+kKya5X7Drtw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T18:36:55.2259966+00:00"}, "GlgSjYdrLE90G0/zVl5q3bnGPc5ndOr+e9L4rXzuAlk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T18:36:55.1769822+00:00"}, "iWx0KPY+KlqvD0kjX90TWlvPMMZWWnhe10eO334gO/8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T18:36:55.1563142+00:00"}, "GBkm4No9eQZ2g7xhRlqURgCxn5tDdVJcWbjR5FSboRU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T18:36:55.1429399+00:00"}, "8rtOcc9uP+UgNOMFgZQPFU/tlUaNbwDIyuvIJ8WFyg8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T18:36:55.2587886+00:00"}, "K8C9Zi1arn+sWPS/kyjKH1aenC8CyasARE/gwcqjyxA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T18:36:55.2557879+00:00"}, "Yl/nEablzlRGs16Vg9Bg+3KjC+uJk0zf24pQaYa8hEY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T18:36:55.254781+00:00"}, "D3zmSjh9FlFWcV8VH8x4z1lnRjVSxtwL9vXzjmWPGaw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T18:36:55.2512657+00:00"}, "TQdAtX0zpXsMVHD45Q0ppwY1iRXGv+xjfHfW+Z/ypWs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T18:36:55.247256+00:00"}, "J88zYRrVmW8dE/N4I5pCfjEtRnQItIxoCmC+n1pYtc0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T18:36:55.2412756+00:00"}, "Lp+fQh+Go8xj6qSk7o/oSch092I1HEhPCfLA1S6FL+4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T18:36:55.2335547+00:00"}, "pWkCcqoKrgN4umT3VGWT7sOE1P3DVJtt/0I1vHZWzuQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T18:36:55.2654303+00:00"}, "3zE8XnlzITnynJmWZb1oVE6TIIABxHIenLNBSlFISds=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T18:36:55.1597968+00:00"}, "OQjGFqp5VOgF97zWFVMLuDnt1/NgfA3fkHAwXDAS7L8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T18:36:55.1419395+00:00"}, "oTKd1WV3dYnoQWCK2Gjoq79mOQrq39YihADCkP29mk8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T18:36:55.2447989+00:00"}, "Q6ScFmx4UDohABHAJDGW3crrT7WL19Y7Ejq2MbfVV28=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T18:36:55.2387704+00:00"}, "rPn7x7hoIan3OgI7Kpeu5SwsaViLywxh8OdfRuf6NHE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T18:36:55.2310503+00:00"}, "UjHopzCMGrXZhacbhB3q2uoVUTa+mipVjx8OEU9D+Vo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T18:36:55.276963+00:00"}, "tMIxXr3qx7zbTStSx1u8D0WePkeB6Mam8wUnEZeUPaA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T18:36:55.2627869+00:00"}, "peItBIqGjajBxtLBOBulLARiv2tZ9rS+s5wk4bN0JFQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T18:36:55.254781+00:00"}, "NM/tGgdFr3QjK4Dgmc76DKwMW4g047d+QJGFCbrFzC8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T18:36:55.2412756+00:00"}, "7XTrQnQSdcD1cF7yPhjl7NpUcIO+L8HGm5dP+qquq88=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T18:36:55.2259966+00:00"}, "lphLTHVtwk5knmKCS/XyRHHrg8EChJVQK31yktBwPN8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T18:36:55.1714024+00:00"}, "l0K6jkBF8K1C8E3Z6cPnjqTULVo3OTeUjHsAYepLakQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T18:36:55.1517818+00:00"}, "psOBNTs0nLvIhK045G1i10WEV0E0n/tsRmr5wNtDTQU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T18:36:55.3491441+00:00"}, "lLQ8fLoVba8DBmRflF8xHPnPb0UInzeeqviuaEp3NYg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T18:36:55.3456837+00:00"}, "thQJ+orEs2DV2hTP+xEACX3soXhW6jeI+EGfGF2J0PE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T18:36:55.289922+00:00"}, "pccK417rt+BD8dBsSFAtK11wKKSm2mX9GmG71gKJO7c=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T18:36:55.2427862+00:00"}, "znkW9Ha2Nqm6S1k3CX5O3QrxyLiDlG6ZoOQE65rIxnQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T18:36:55.239769+00:00"}, "GMETogru1Q86pZA+DUYc5da494akIorucSyKEN8V/Cg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T18:36:55.2377611+00:00"}, "WtKWcN+v0yLCHNNlbeAzW3nBra2t/EPx5T2eXs9Mnlg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T18:36:55.2330484+00:00"}, "IkexHY4wVtSB3NQMVDsdaVHKrMSM7rMBEcPd9PN7lzQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T18:36:55.2879226+00:00"}, "O5txG+3RSXi/6vtFyNniwhStgYj/sDVtSedm410SwQs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T18:36:55.2597911+00:00"}, "2XAzkYCcvedoJyW6Htbbios68B99AZwhhegyFViai4g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T18:36:55.1553127+00:00"}, "b0iK5BoogNEDS7HckbWxNZLmzSgA/uBHFv2AJrD2G3g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T18:36:55.2290398+00:00"}, "O/Vp7c2/Q0jh2Kqv/0biSKryS5KLjFRf87/QvP3uhuY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T18:36:55.2269947+00:00"}, "Nygvm97iyQtpyPV/BR/Ufd9K3C1krbDwnsBDZOPa6fw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T18:36:55.2214706+00:00"}, "EaufjAjjVWEVDMS+ajitze2hGKLwUJiezy6e40s0mqA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T18:36:55.247256+00:00"}, "vPIq7er6UogqDesqH9DGWd942+mGk4baCReAYSFLv34=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T18:36:55.1714024+00:00"}, "tIjBLKxGn+d4WhmJYBQek1qfJ8Z4TE6Z76ZApcSLhxc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T18:36:55.1694018+00:00"}, "hz13F0ZxLcp+P1VPJX8qCOGlIvGutSYlS5lEt1VlibA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T18:36:55.1673937+00:00"}, "6Ubyi3VlJfg1M7LUaSf1ERS1RPe8lP7MJjBT/5Rf2Pw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T18:36:55.1597968+00:00"}, "zmiiXi2XKsM+l/ViWzq1UVJBMd37+R1YC7CBXJj6K2c=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T18:36:55.144772+00:00"}, "ZCYkNU4/H58DI+yAeG/tD15Ht1nOAALbkQZtC55SWD4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T18:36:55.1419395+00:00"}, "QaHSwgW+gbJGr/8/rnOGU/N2IPy8zrOezr5qlo1skNg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T18:36:55.2164682+00:00"}, "sd3pgTibTwTCXiQEshdXC1i07S+k+Yw86fExd3WfNzY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T18:36:55.1789825+00:00"}, "Mdm/l0kVv+e305aYSjaeE1FjOFliKNq2jQ5OQ7sdffo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T18:36:55.1749236+00:00"}, "b4JUa9z6pFltL1FuvzwKc1oOma2DAMQvgBJEF8JEcFY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T18:36:55.2279946+00:00"}, "8Swz1Lt6a6H8pqGcprsAvltyuns190vB9JADB36ObIE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T18:36:55.2239878+00:00"}, "s7R9hXWUmvI75Eax5uDnFDhP2JaYRCC0StVAD6Erw10=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T18:36:55.1618047+00:00"}, "uq6+GPgs1pehiXOC61du65HYAispQyT9SuQx4G+lX0c=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T18:36:55.1553127+00:00"}, "Aw7UufjQ7OQNHgh2RfDhmZpiu4HX+zh1HWCe1n9a+fk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-16T18:36:55.1409331+00:00"}, "1cbOjJog5UxMisVWH3vqt5FAwi2zu3VZP+JHY54uS3Y=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-16T18:36:55.1340562+00:00"}}, "CachedCopyCandidates": {}}