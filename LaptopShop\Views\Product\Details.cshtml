@model LaptopShop.Models.ProductDetailViewModel
@{
    ViewData["Title"] = Model.Laptop.Name;
}

<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="Product" asp-action="Index">Sản phẩm</a></li>
            <li class="breadcrumb-item"><a asp-controller="Product" asp-action="Index" asp-route-categoryId="@Model.Laptop.CategoryId">@Model.Laptop.Category.Name</a></li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Laptop.Name</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Images -->
        <div class="col-lg-6 mb-4">
            <div class="product-image-section">
                <div class="main-image mb-3">
                    @if (!string.IsNullOrEmpty(Model.Laptop.MainImageUrl))
                    {
                        <img src="@Model.Laptop.MainImageUrl" class="img-fluid rounded shadow" alt="@Model.Laptop.Name" style="width: 100%; height: 400px; object-fit: cover;">
                    }
                    else
                    {
                        <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 400px;">
                            <i class="fas fa-laptop fa-5x text-muted float-animation"></i>
                        </div>
                    }
                </div>
                
                <!-- Badges -->
                <div class="d-flex gap-2 mb-3">
                    @if (Model.Laptop.IsFeatured)
                    {
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-star me-1"></i>Sản phẩm nổi bật
                        </span>
                    }
                    @if (Model.Laptop.SalePrice.HasValue)
                    {
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-percentage me-1"></i>Giảm @(Math.Round((1 - Model.Laptop.SalePrice.Value / Model.Laptop.Price) * 100))%
                        </span>
                    }
                    @if (Model.Laptop.StockQuantity <= 5)
                    {
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-exclamation-triangle me-1"></i>Chỉ còn @Model.Laptop.StockQuantity sản phẩm
                        </span>
                    }
                </div>
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-lg-6">
            <div class="product-info">
                <h1 class="gradient-text mb-3">@Model.Laptop.Name</h1>
                
                <div class="product-meta mb-4">
                    <p class="mb-2">
                        <strong>Thương hiệu:</strong> 
                        <a asp-controller="Product" asp-action="Index" asp-route-brandId="@Model.Laptop.BrandId" class="text-decoration-none">
                            @Model.Laptop.Brand.Name
                        </a>
                    </p>
                    <p class="mb-2">
                        <strong>Danh mục:</strong> 
                        <a asp-controller="Product" asp-action="Index" asp-route-categoryId="@Model.Laptop.CategoryId" class="text-decoration-none">
                            @Model.Laptop.Category.Name
                        </a>
                    </p>
                    @if (!string.IsNullOrEmpty(Model.Laptop.Model))
                    {
                        <p class="mb-2"><strong>Model:</strong> @Model.Laptop.Model</p>
                    }
                </div>

                <!-- Price Section -->
                <div class="price-section mb-4 p-3 bg-light rounded">
                    @if (Model.Laptop.SalePrice.HasValue)
                    {
                        <div class="d-flex align-items-center mb-2">
                            <span class="h3 text-danger mb-0 me-3">@Model.Laptop.SalePrice.Value.ToString("N0") ₫</span>
                            <span class="h5 text-muted text-decoration-line-through mb-0">@Model.Laptop.Price.ToString("N0") ₫</span>
                        </div>
                        <p class="text-success mb-0">
                            <i class="fas fa-piggy-bank me-1"></i>
                            Tiết kiệm: @((Model.Laptop.Price - Model.Laptop.SalePrice.Value).ToString("N0")) ₫
                        </p>
                    }
                    else
                    {
                        <span class="h3 text-primary mb-0">@Model.Laptop.Price.ToString("N0") ₫</span>
                    }
                </div>

                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    @if (Model.Laptop.StockQuantity > 0)
                    {
                        <p class="text-success mb-0">
                            <i class="fas fa-check-circle me-1"></i>
                            <strong>Còn hàng</strong> (@Model.Laptop.StockQuantity sản phẩm)
                        </p>
                    }
                    else
                    {
                        <p class="text-danger mb-0">
                            <i class="fas fa-times-circle me-1"></i>
                            <strong>Hết hàng</strong>
                        </p>
                    }
                </div>

                <!-- Add to Cart -->
                <div class="add-to-cart mb-4">
                    @if (Model.Laptop.StockQuantity > 0)
                    {
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="input-group">
                                    <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
                                    <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="@Model.Laptop.StockQuantity">
                                    <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <button class="btn btn-primary btn-lg w-100" onclick="addToCartWithQuantity(@Model.Laptop.Id)">
                                    <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <button class="btn btn-secondary btn-lg w-100" disabled>
                            <i class="fas fa-times me-2"></i>Hết hàng
                        </button>
                    }
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions mb-4">
                    <div class="row">
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100">
                                <i class="fas fa-heart me-1"></i>Yêu thích
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-info w-100">
                                <i class="fas fa-share-alt me-1"></i>Chia sẻ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Shipping Info -->
                <div class="shipping-info bg-light p-3 rounded">
                    <h6 class="mb-2"><i class="fas fa-shipping-fast me-2"></i>Thông tin giao hàng</h6>
                    <ul class="list-unstyled mb-0 small">
                        <li><i class="fas fa-check text-success me-1"></i>Miễn phí giao hàng cho đơn từ 500.000₫</li>
                        <li><i class="fas fa-check text-success me-1"></i>Giao hàng trong 1-3 ngày làm việc</li>
                        <li><i class="fas fa-check text-success me-1"></i>Bảo hành chính hãng</li>
                        <li><i class="fas fa-check text-success me-1"></i>Đổi trả trong 7 ngày</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Details Tabs -->
    <div class="row mt-5">
        <div class="col-12">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab">
                        <i class="fas fa-info-circle me-1"></i>Mô tả sản phẩm
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="specs-tab" data-bs-toggle="tab" data-bs-target="#specs" type="button" role="tab">
                        <i class="fas fa-cogs me-1"></i>Thông số kỹ thuật
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                        <i class="fas fa-star me-1"></i>Đánh giá (@Model.TotalReviews)
                    </button>
                </li>
            </ul>
            
            <div class="tab-content mt-3" id="productTabsContent">
                <!-- Description Tab -->
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <div class="p-4">
                        <h5>Mô tả chi tiết</h5>
                        <p>@Model.Laptop.Description</p>
                        
                        @if (!string.IsNullOrEmpty(Model.Laptop.Color))
                        {
                            <p><strong>Màu sắc:</strong> @Model.Laptop.Color</p>
                        }
                        @if (!string.IsNullOrEmpty(Model.Laptop.Weight))
                        {
                            <p><strong>Trọng lượng:</strong> @Model.Laptop.Weight</p>
                        }
                    </div>
                </div>

                <!-- Specifications Tab -->
                <div class="tab-pane fade" id="specs" role="tabpanel">
                    <div class="p-4">
                        <h5>Thông số kỹ thuật</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <tbody>
                                    @if (!string.IsNullOrEmpty(Model.Laptop.Processor))
                                    {
                                        <tr>
                                            <td><strong>Bộ xử lý (CPU)</strong></td>
                                            <td>@Model.Laptop.Processor</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.RAM))
                                    {
                                        <tr>
                                            <td><strong>Bộ nhớ RAM</strong></td>
                                            <td>@Model.Laptop.RAM</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.Storage))
                                    {
                                        <tr>
                                            <td><strong>Ổ cứng</strong></td>
                                            <td>@Model.Laptop.Storage</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.GraphicsCard))
                                    {
                                        <tr>
                                            <td><strong>Card đồ họa</strong></td>
                                            <td>@Model.Laptop.GraphicsCard</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.ScreenSize))
                                    {
                                        <tr>
                                            <td><strong>Kích thước màn hình</strong></td>
                                            <td>@Model.Laptop.ScreenSize</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.Resolution))
                                    {
                                        <tr>
                                            <td><strong>Độ phân giải</strong></td>
                                            <td>@Model.Laptop.Resolution</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.OperatingSystem))
                                    {
                                        <tr>
                                            <td><strong>Hệ điều hành</strong></td>
                                            <td>@Model.Laptop.OperatingSystem</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.BatteryLife))
                                    {
                                        <tr>
                                            <td><strong>Thời lượng pin</strong></td>
                                            <td>@Model.Laptop.BatteryLife</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Laptop.Connectivity))
                                    {
                                        <tr>
                                            <td><strong>Kết nối</strong></td>
                                            <td>@Model.Laptop.Connectivity</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="p-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center mb-4">
                                    <h2 class="gradient-text">@Model.AverageRating.ToString("F1")</h2>
                                    <div class="mb-2">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= Model.AverageRating)
                                            {
                                                <i class="fas fa-star text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star text-muted"></i>
                                            }
                                        }
                                    </div>
                                    <p class="text-muted">@Model.TotalReviews đánh giá</p>
                                </div>
                            </div>
                            <div class="col-md-8">
                                @if (Model.CanReview)
                                {
                                    <button class="btn btn-primary mb-3">
                                        <i class="fas fa-edit me-1"></i>Viết đánh giá
                                    </button>
                                }
                                
                                @if (Model.Reviews.Any())
                                {
                                    @foreach (var review in Model.Reviews.Take(5))
                                    {
                                        <div class="review-item border-bottom pb-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">@review.User.FullName</h6>
                                                    <div class="mb-2">
                                                        @for (int i = 1; i <= 5; i++)
                                                        {
                                                            if (i <= review.Rating)
                                                            {
                                                                <i class="fas fa-star text-warning"></i>
                                                            }
                                                            else
                                                            {
                                                                <i class="far fa-star text-muted"></i>
                                                            }
                                                        }
                                                    </div>
                                                    @if (!string.IsNullOrEmpty(review.Title))
                                                    {
                                                        <h6>@review.Title</h6>
                                                    }
                                                    @if (!string.IsNullOrEmpty(review.Comment))
                                                    {
                                                        <p class="mb-0">@review.Comment</p>
                                                    }
                                                </div>
                                                <small class="text-muted">@review.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <p class="text-muted">Chưa có đánh giá nào cho sản phẩm này.</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    @if (Model.RelatedLaptops.Any())
    {
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="gradient-text mb-4">Sản phẩm liên quan</h3>
                <div class="row">
                    @foreach (var laptop in Model.RelatedLaptops)
                    {
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 shadow-sm product-card">
                                <div class="position-relative img-hover-zoom">
                                    @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                                    {
                                        <img src="@laptop.MainImageUrl" class="card-img-top" alt="@laptop.Name" style="height: 200px; object-fit: cover;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <i class="fas fa-laptop fa-2x text-muted"></i>
                                        </div>
                                    }
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">@laptop.Name</h6>
                                    <p class="text-muted small">@laptop.Brand.Name</p>
                                    <div class="price-section">
                                        @if (laptop.SalePrice.HasValue)
                                        {
                                            <span class="h6 text-danger">@laptop.SalePrice.Value.ToString("N0") ₫</span>
                                            <span class="text-muted text-decoration-line-through ms-2 small">@laptop.Price.ToString("N0") ₫</span>
                                        }
                                        else
                                        {
                                            <span class="h6 text-primary">@laptop.Price.ToString("N0") ₫</span>
                                        }
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <a href="@Url.Action("Details", new { id = laptop.Id })" class="btn btn-outline-primary btn-sm w-100">
                                        Xem chi tiết
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        function increaseQuantity() {
            var quantityInput = document.getElementById('quantity');
            var currentValue = parseInt(quantityInput.value);
            var maxValue = parseInt(quantityInput.max);
            
            if (currentValue < maxValue) {
                quantityInput.value = currentValue + 1;
            }
        }

        function decreaseQuantity() {
            var quantityInput = document.getElementById('quantity');
            var currentValue = parseInt(quantityInput.value);
            var minValue = parseInt(quantityInput.min);
            
            if (currentValue > minValue) {
                quantityInput.value = currentValue - 1;
            }
        }

        function addToCartWithQuantity(laptopId) {
            var quantity = parseInt(document.getElementById('quantity').value);
            addToCart(laptopId, quantity);
        }
    </script>
}
