@model LaptopShop.Models.ProductDetailViewModel
@{
    ViewData["Title"] = Model.Laptop.Name;
}

<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="Product" asp-action="Index">Sản phẩm</a></li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Laptop.Name</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Images -->
        <div class="col-lg-6 mb-4">
            <div class="product-image-section">
                <div class="main-image mb-3">
                    @if (!string.IsNullOrEmpty(Model.Laptop.MainImageUrl))
                    {
                        <img src="@Model.Laptop.MainImageUrl" class="img-fluid rounded shadow" alt="@Model.Laptop.Name" style="width: 100%; height: 400px; object-fit: cover;">
                    }
                    else
                    {
                        <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 400px;">
                            <i class="fas fa-laptop fa-5x text-muted float-animation"></i>
                        </div>
                    }
                </div>

                <!-- Badges -->
                <div class="d-flex gap-2 mb-3">
                    @if (Model.Laptop.IsFeatured)
                    {
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-star me-1"></i>Sản phẩm nổi bật
                        </span>
                    }
                    @if (Model.Laptop.SalePrice.HasValue)
                    {
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-percentage me-1"></i>Giảm @(Math.Round((1 - Model.Laptop.SalePrice.Value / Model.Laptop.Price) * 100))%
                        </span>
                    }
                    @if (Model.Laptop.StockQuantity <= 5)
                    {
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-exclamation-triangle me-1"></i>Chỉ còn @Model.Laptop.StockQuantity sản phẩm
                        </span>
                    }
                </div>
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-lg-6">
            <div class="product-info">
                <h1 class="gradient-text mb-3">@Model.Laptop.Name</h1>

                <div class="product-meta mb-4">
                    <p class="mb-2">
                        <strong>Thương hiệu:</strong> @Model.Laptop.Brand.Name
                    </p>
                    <p class="mb-2">
                        <strong>Danh mục:</strong> @Model.Laptop.Category.Name
                    </p>
                    @if (!string.IsNullOrEmpty(Model.Laptop.Model))
                    {
                        <p class="mb-2"><strong>Model:</strong> @Model.Laptop.Model</p>
                    }
                </div>

                <!-- Price Section -->
                <div class="price-section mb-4 p-3 bg-light rounded">
                    @if (Model.Laptop.SalePrice.HasValue)
                    {
                        <div class="d-flex align-items-center mb-2">
                            <span class="h3 text-danger mb-0 me-3">@Model.Laptop.SalePrice.Value.ToString("N0") ₫</span>
                            <span class="h5 text-muted text-decoration-line-through mb-0">@Model.Laptop.Price.ToString("N0") ₫</span>
                        </div>
                        <p class="text-success mb-0">
                            <i class="fas fa-piggy-bank me-1"></i>
                            Tiết kiệm: @((Model.Laptop.Price - Model.Laptop.SalePrice.Value).ToString("N0")) ₫
                        </p>
                    }
                    else
                    {
                        <span class="h3 text-primary mb-0">@Model.Laptop.Price.ToString("N0") ₫</span>
                    }
                </div>

                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    @if (Model.Laptop.StockQuantity > 0)
                    {
                        <p class="text-success mb-0">
                            <i class="fas fa-check-circle me-1"></i>
                            <strong>Còn hàng</strong> (@Model.Laptop.StockQuantity sản phẩm)
                        </p>
                    }
                    else
                    {
                        <p class="text-danger mb-0">
                            <i class="fas fa-times-circle me-1"></i>
                            <strong>Hết hàng</strong>
                        </p>
                    }
                </div>

                <!-- Add to Cart -->
                <div class="add-to-cart mb-4">
                    @if (Model.Laptop.StockQuantity > 0)
                    {
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="input-group">
                                    <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
                                    <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="@Model.Laptop.StockQuantity">
                                    <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <button class="btn btn-primary btn-lg w-100" onclick="addToCartWithQuantity(@Model.Laptop.Id)">
                                    <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <button class="btn btn-secondary btn-lg w-100" disabled>
                            <i class="fas fa-times me-2"></i>Hết hàng
                        </button>
                    }
                </div>

                <!-- Shipping Info -->
                <div class="shipping-info bg-light p-3 rounded">
                    <h6 class="mb-2"><i class="fas fa-shipping-fast me-2"></i>Thông tin giao hàng</h6>
                    <ul class="list-unstyled mb-0 small">
                        <li><i class="fas fa-check text-success me-1"></i>Miễn phí giao hàng cho đơn từ 500.000₫</li>
                        <li><i class="fas fa-check text-success me-1"></i>Giao hàng trong 1-3 ngày làm việc</li>
                        <li><i class="fas fa-check text-success me-1"></i>Bảo hành chính hãng</li>
                        <li><i class="fas fa-check text-success me-1"></i>Đổi trả trong 7 ngày</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>