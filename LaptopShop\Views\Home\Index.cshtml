@model LaptopShop.Models.HomeViewModel
@{
    ViewData["Title"] = "Trang Chủ";
}

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Cửa Hàng Laptop Uy Tín #1</h1>
                <p class="lead mb-4">Chuyên cung cấp laptop chính hãng với giá tốt nhất thị trường. <PERSON><PERSON><PERSON> hành ch<PERSON> hãng, giao hàng toàn quốc.</p>
                <a href="@Url.Action("Index", "Product")" class="btn btn-light btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i><PERSON><PERSON>
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-laptop display-1"></i>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section mb-5">
    <div class="container">
        <h2 class="text-center mb-4">Danh Mục Sản Phẩm</h2>
        <div class="row">
            @foreach (var category in Model.Categories)
            {
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="card h-100 shadow-sm category-card">
                        <div class="card-body text-center">
                            <i class="fas fa-laptop-code fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">@category.Name</h5>
                            <p class="card-text text-muted">@category.Description</p>
                            <a href="@Url.Action("Index", "Product", new { categoryId = category.Id })" class="btn btn-outline-primary">
                                Xem Sản Phẩm
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-products mb-5">
    <div class="container">
        <h2 class="text-center mb-4">Sản Phẩm Nổi Bật</h2>
        <div class="row">
            @foreach (var laptop in Model.FeaturedLaptops)
            {
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="card h-100 shadow-sm product-card">
                        <div class="position-relative img-hover-zoom">
                            @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                            {
                                <img src="@laptop.MainImageUrl" class="card-img-top" alt="@laptop.Name" style="height: 220px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 220px;">
                                    <i class="fas fa-laptop fa-3x text-muted float-animation"></i>
                                </div>
                            }
                            @if (laptop.SalePrice.HasValue)
                            {
                                <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                                    <i class="fas fa-percentage me-1"></i>-@(Math.Round((1 - laptop.SalePrice.Value / laptop.Price) * 100))%
                                </span>
                            }
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">@laptop.Name</h6>
                            <p class="text-muted small">@laptop.Brand.Name</p>
                            <div class="price-section mb-3">
                                @if (laptop.SalePrice.HasValue)
                                {
                                    <span class="h6 text-danger">@laptop.SalePrice.Value.ToString("N0") ₫</span>
                                    <span class="text-muted text-decoration-line-through ms-2">@laptop.Price.ToString("N0") ₫</span>
                                }
                                else
                                {
                                    <span class="h6 text-primary">@laptop.Price.ToString("N0") ₫</span>
                                }
                            </div>
                            <div class="d-grid gap-2">
                                <a href="@Url.Action("Details", "Product", new { id = laptop.Id })" class="btn btn-outline-primary btn-sm">
                                    Xem Chi Tiết
                                </a>
                                <button class="btn btn-primary btn-sm" onclick="addToCart(@laptop.Id)">
                                    <i class="fas fa-cart-plus me-1"></i>Thêm Vào Giỏ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="text-center mt-4">
            <a href="@Url.Action("Index", "Product")" class="btn btn-primary btn-lg">
                Xem Tất Cả Sản Phẩm
            </a>
        </div>
    </div>
</section>

<!-- New Products Section -->
<section class="new-products mb-5 bg-light py-5">
    <div class="container">
        <h2 class="text-center mb-4">Sản Phẩm Mới Nhất</h2>
        <div class="row">
            @foreach (var laptop in Model.NewLaptops)
            {
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-img-top position-relative">
                            @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                            {
                                <img src="@laptop.MainImageUrl" class="card-img-top" alt="@laptop.Name" style="height: 180px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="bg-secondary d-flex align-items-center justify-content-center" style="height: 180px;">
                                    <i class="fas fa-laptop fa-2x text-white"></i>
                                </div>
                            }
                            <span class="badge bg-success position-absolute top-0 start-0 m-2">Mới</span>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">@laptop.Name</h6>
                            <p class="text-muted small">@laptop.Brand.Name</p>
                            <div class="price-section">
                                @if (laptop.SalePrice.HasValue)
                                {
                                    <span class="h6 text-danger">@laptop.SalePrice.Value.ToString("N0") ₫</span>
                                    <span class="text-muted text-decoration-line-through ms-2">@laptop.Price.ToString("N0") ₫</span>
                                }
                                else
                                {
                                    <span class="h6 text-primary">@laptop.Price.ToString("N0") ₫</span>
                                }
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-grid gap-2">
                                <a href="@Url.Action("Details", "Product", new { id = laptop.Id })" class="btn btn-outline-primary btn-sm">
                                    Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Brands Section -->
<section class="brands-section mb-5">
    <div class="container">
        <h2 class="text-center mb-4">Thương Hiệu Nổi Tiếng</h2>
        <div class="row">
            @foreach (var brand in Model.PopularBrands)
            {
                <div class="col-6 col-md-4 col-lg-2 mb-3">
                    <div class="card text-center h-100 shadow-sm brand-card">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <a href="@Url.Action("Index", "Product", new { brandId = brand.Id })" class="text-decoration-none">
                                <h6 class="mb-0">@brand.Name</h6>
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>
