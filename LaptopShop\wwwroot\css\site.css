html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom Styles for LaptopShop */

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
}

/* Product Cards */
.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-card {
  transition: transform 0.3s ease;
  border: none;
}

.category-card:hover {
  transform: translateY(-3px);
}

.brand-card {
  transition: transform 0.3s ease;
  border: none;
}

.brand-card:hover {
  transform: scale(1.05);
}

/* Price styling */
.price-section {
  font-weight: 600;
}

/* Cart badge */
#cart-count {
  font-size: 0.7rem;
  min-width: 1.2rem;
  height: 1.2rem;
  display: none;
}

/* Navbar */
.navbar-brand {
  font-size: 1.5rem;
}

/* Search form */
.navbar .input-group {
  width: 300px;
}

@media (max-width: 768px) {
  .navbar .input-group {
    width: 100%;
    margin-top: 1rem;
  }
}

/* Product grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Filters */
.filters-sidebar {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

/* Pagination */
.pagination .page-link {
  color: #0d6efd;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Admin styles */
.admin-sidebar {
  background-color: #343a40;
  min-height: calc(100vh - 56px);
}

.admin-sidebar .nav-link {
  color: #adb5bd;
  padding: 0.75rem 1rem;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
  color: #fff;
  background-color: #495057;
}

/* Dashboard cards */
.dashboard-card {
  border-left: 4px solid #0d6efd;
}

.dashboard-card.success {
  border-left-color: #198754;
}

.dashboard-card.warning {
  border-left-color: #ffc107;
}

.dashboard-card.danger {
  border-left-color: #dc3545;
}

/* Tables */
.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Forms */
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 0.5rem;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive utilities */
@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section .lead {
    font-size: 1rem;
  }
}