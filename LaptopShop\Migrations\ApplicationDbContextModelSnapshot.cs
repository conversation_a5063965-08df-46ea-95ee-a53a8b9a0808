﻿// <auto-generated />
using System;
using LaptopShop.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace LaptopShop.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("LaptopShop.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("LaptopShop.Models.Brand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Brands");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop hàng đầu từ Đài Loan",
                            IsActive = true,
                            Name = "ASUS"
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop nổi tiếng từ Mỹ",
                            IsActive = true,
                            Name = "Dell"
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop uy tín từ Mỹ",
                            IsActive = true,
                            Name = "HP"
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop từ Trung Quốc",
                            IsActive = true,
                            Name = "Lenovo"
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop từ Đài Loan",
                            IsActive = true,
                            Name = "Acer"
                        },
                        new
                        {
                            Id = 6,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu laptop gaming nổi tiếng",
                            IsActive = true,
                            Name = "MSI"
                        },
                        new
                        {
                            Id = 7,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Thương hiệu MacBook từ Mỹ",
                            IsActive = true,
                            Name = "Apple"
                        });
                });

            modelBuilder.Entity("LaptopShop.Models.CartItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("LaptopId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("LaptopId");

                    b.HasIndex("UserId");

                    b.ToTable("CartItems");
                });

            modelBuilder.Entity("LaptopShop.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop chuyên dụng cho game thủ",
                            IsActive = true,
                            Name = "Laptop Gaming"
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop phù hợp cho công việc văn phòng",
                            IsActive = true,
                            Name = "Laptop Văn Phòng"
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop chuyên dụng cho thiết kế đồ họa",
                            IsActive = true,
                            Name = "Laptop Đồ Họa"
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop siêu mỏng nhẹ, dễ di chuyển",
                            IsActive = true,
                            Name = "Laptop Mỏng Nhẹ"
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop hiệu năng cao cho chuyên gia",
                            IsActive = true,
                            Name = "Laptop Workstation"
                        });
                });

            modelBuilder.Entity("LaptopShop.Models.Laptop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatteryLife")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("BrandId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Color")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Connectivity")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("GraphicsCard")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageUrls")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MainImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("OperatingSystem")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Processor")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("RAM")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Resolution")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ScreenSize")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Storage")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Weight")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BrandId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Name");

                    b.ToTable("Laptops");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            BatteryLife = "6-8 giờ",
                            BrandId = 1,
                            CategoryId = 1,
                            Color = "Đen",
                            Connectivity = "WiFi 6, Bluetooth 5.1, USB-C, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop gaming mạnh mẽ với AMD Ryzen 7 và RTX 3060",
                            GraphicsCard = "NVIDIA RTX 3060 6GB",
                            IsActive = true,
                            IsFeatured = true,
                            Model = "G513QM",
                            Name = "ASUS ROG Strix G15",
                            OperatingSystem = "Windows 11",
                            Price = 25990000m,
                            Processor = "AMD Ryzen 7 5800H",
                            RAM = "16GB DDR4",
                            Resolution = "1920x1080 144Hz",
                            SalePrice = 23990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 15,
                            Storage = "512GB SSD NVMe",
                            Weight = "2.3kg"
                        },
                        new
                        {
                            Id = 2,
                            BatteryLife = "10-12 giờ",
                            BrandId = 2,
                            CategoryId = 4,
                            Color = "Bạc",
                            Connectivity = "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop mỏng nhẹ cao cấp cho doanh nhân",
                            GraphicsCard = "Intel Iris Xe",
                            IsActive = true,
                            IsFeatured = true,
                            Model = "9320",
                            Name = "Dell XPS 13",
                            OperatingSystem = "Windows 11",
                            Price = 32990000m,
                            Processor = "Intel Core i7-1260P",
                            RAM = "16GB LPDDR5",
                            Resolution = "1920x1200",
                            SalePrice = 29990000m,
                            ScreenSize = "13.4 inch",
                            StockQuantity = 10,
                            Storage = "512GB SSD NVMe",
                            Weight = "1.27kg"
                        },
                        new
                        {
                            Id = 3,
                            BatteryLife = "5-7 giờ",
                            BrandId = 3,
                            CategoryId = 1,
                            Color = "Xanh đen",
                            Connectivity = "WiFi 5, Bluetooth 5.0, USB-A, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop gaming giá rẻ hiệu năng tốt",
                            GraphicsCard = "NVIDIA GTX 1650 4GB",
                            IsActive = true,
                            IsFeatured = false,
                            Model = "15-dk2000",
                            Name = "HP Pavilion Gaming 15",
                            OperatingSystem = "Windows 11",
                            Price = 18990000m,
                            Processor = "Intel Core i5-11300H",
                            RAM = "8GB DDR4",
                            Resolution = "1920x1080",
                            SalePrice = 16990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 20,
                            Storage = "512GB SSD",
                            Weight = "2.23kg"
                        },
                        new
                        {
                            Id = 4,
                            BatteryLife = "8-10 giờ",
                            BrandId = 4,
                            CategoryId = 2,
                            Color = "Đen",
                            Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop văn phòng bền bỉ cho doanh nghiệp",
                            GraphicsCard = "Intel Iris Xe",
                            IsActive = true,
                            IsFeatured = false,
                            Model = "Gen 4",
                            Name = "Lenovo ThinkPad E15",
                            OperatingSystem = "Windows 11 Pro",
                            Price = 21990000m,
                            Processor = "Intel Core i5-1235U",
                            RAM = "8GB DDR4",
                            Resolution = "1920x1080",
                            ScreenSize = "15.6 inch",
                            StockQuantity = 12,
                            Storage = "256GB SSD",
                            Weight = "1.7kg"
                        },
                        new
                        {
                            Id = 5,
                            BatteryLife = "7-9 giờ",
                            BrandId = 5,
                            CategoryId = 2,
                            Color = "Bạc",
                            Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop học tập và làm việc giá tốt",
                            GraphicsCard = "Intel Iris Xe",
                            IsActive = true,
                            IsFeatured = true,
                            Model = "A515-57",
                            Name = "Acer Aspire 5",
                            OperatingSystem = "Windows 11",
                            Price = 14990000m,
                            Processor = "Intel Core i5-1235U",
                            RAM = "8GB DDR4",
                            Resolution = "1920x1080",
                            SalePrice = 13990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 25,
                            Storage = "512GB SSD",
                            Weight = "1.7kg"
                        },
                        new
                        {
                            Id = 6,
                            BatteryLife = "5-7 giờ",
                            BrandId = 6,
                            CategoryId = 1,
                            Color = "Đen",
                            Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop gaming mỏng nhẹ với GTX 1650",
                            GraphicsCard = "NVIDIA GTX 1650 4GB",
                            IsActive = true,
                            IsFeatured = false,
                            Model = "Thin 11UC",
                            Name = "MSI Gaming GF63",
                            OperatingSystem = "Windows 11",
                            Price = 19990000m,
                            Processor = "Intel Core i5-11400H",
                            RAM = "8GB DDR4",
                            Resolution = "1920x1080",
                            SalePrice = 17990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 18,
                            Storage = "512GB SSD",
                            Weight = "1.86kg"
                        },
                        new
                        {
                            Id = 7,
                            BatteryLife = "15-18 giờ",
                            BrandId = 7,
                            CategoryId = 4,
                            Color = "Bạc",
                            Connectivity = "WiFi 6, Bluetooth 5.0, USB-C Thunderbolt",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop Apple với chip M2 mạnh mẽ và tiết kiệm pin",
                            GraphicsCard = "Apple M2 8-core GPU",
                            IsActive = true,
                            IsFeatured = true,
                            Model = "2022",
                            Name = "MacBook Air M2",
                            OperatingSystem = "macOS",
                            Price = 32990000m,
                            Processor = "Apple M2 8-core",
                            RAM = "8GB Unified Memory",
                            Resolution = "2560x1664 Retina",
                            SalePrice = 29990000m,
                            ScreenSize = "13.6 inch",
                            StockQuantity = 8,
                            Storage = "256GB SSD",
                            Weight = "1.24kg"
                        },
                        new
                        {
                            Id = 8,
                            BatteryLife = "6-8 giờ",
                            BrandId = 1,
                            CategoryId = 2,
                            Color = "Xanh",
                            Connectivity = "WiFi 5, Bluetooth 4.2, USB-A, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop văn phòng giá rẻ hiệu năng ổn định",
                            GraphicsCard = "Intel UHD Graphics",
                            IsActive = true,
                            IsFeatured = false,
                            Model = "X1500EA",
                            Name = "ASUS VivoBook 15",
                            OperatingSystem = "Windows 11",
                            Price = 12990000m,
                            Processor = "Intel Core i3-1115G4",
                            RAM = "4GB DDR4",
                            Resolution = "1920x1080",
                            SalePrice = 11990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 30,
                            Storage = "256GB SSD",
                            Weight = "1.8kg"
                        },
                        new
                        {
                            Id = 9,
                            BatteryLife = "5-7 giờ",
                            BrandId = 2,
                            CategoryId = 2,
                            Color = "Đen",
                            Connectivity = "WiFi 5, Bluetooth 5.0, USB-A, HDMI",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop cơ bản cho học sinh sinh viên",
                            GraphicsCard = "Intel UHD Graphics",
                            IsActive = true,
                            IsFeatured = false,
                            Model = "3511",
                            Name = "Dell Inspiron 15 3000",
                            OperatingSystem = "Windows 11",
                            Price = 11990000m,
                            Processor = "Intel Core i3-1115G4",
                            RAM = "4GB DDR4",
                            Resolution = "1366x768",
                            ScreenSize = "15.6 inch",
                            StockQuantity = 22,
                            Storage = "256GB SSD",
                            Weight = "1.83kg"
                        },
                        new
                        {
                            Id = 10,
                            BatteryLife = "8-10 giờ",
                            BrandId = 3,
                            CategoryId = 3,
                            Color = "Xám",
                            Connectivity = "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Laptop workstation chuyên nghiệp cho thiết kế đồ họa",
                            GraphicsCard = "NVIDIA RTX A2000 4GB",
                            IsActive = true,
                            IsFeatured = true,
                            Model = "Mobile Workstation",
                            Name = "HP ZBook Studio G8",
                            OperatingSystem = "Windows 11 Pro",
                            Price = 45990000m,
                            Processor = "Intel Core i7-11800H",
                            RAM = "32GB DDR4",
                            Resolution = "3840x2160 4K",
                            SalePrice = 42990000m,
                            ScreenSize = "15.6 inch",
                            StockQuantity = 5,
                            Storage = "1TB SSD NVMe",
                            Weight = "1.79kg"
                        });
                });

            modelBuilder.Entity("LaptopShop.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ShippingAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ShippingFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("LaptopShop.Models.OrderDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("LaptopId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("OrderId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ProductName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductSpecs")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("LaptopId");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderDetails");
                });

            modelBuilder.Entity("LaptopShop.Models.Review", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("INTEGER");

                    b.Property<int>("LaptopId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Rating")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("LaptopId");

                    b.HasIndex("UserId");

                    b.ToTable("Reviews");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("LaptopShop.Models.CartItem", b =>
                {
                    b.HasOne("LaptopShop.Models.Laptop", "Laptop")
                        .WithMany("CartItems")
                        .HasForeignKey("LaptopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LaptopShop.Models.ApplicationUser", "User")
                        .WithMany("CartItems")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Laptop");

                    b.Navigation("User");
                });

            modelBuilder.Entity("LaptopShop.Models.Laptop", b =>
                {
                    b.HasOne("LaptopShop.Models.Brand", "Brand")
                        .WithMany("Laptops")
                        .HasForeignKey("BrandId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LaptopShop.Models.Category", "Category")
                        .WithMany("Laptops")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Brand");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("LaptopShop.Models.Order", b =>
                {
                    b.HasOne("LaptopShop.Models.ApplicationUser", "User")
                        .WithMany("Orders")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LaptopShop.Models.OrderDetail", b =>
                {
                    b.HasOne("LaptopShop.Models.Laptop", "Laptop")
                        .WithMany("OrderDetails")
                        .HasForeignKey("LaptopId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LaptopShop.Models.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Laptop");

                    b.Navigation("Order");
                });

            modelBuilder.Entity("LaptopShop.Models.Review", b =>
                {
                    b.HasOne("LaptopShop.Models.Laptop", "Laptop")
                        .WithMany("Reviews")
                        .HasForeignKey("LaptopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LaptopShop.Models.ApplicationUser", "User")
                        .WithMany("Reviews")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Laptop");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("LaptopShop.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("LaptopShop.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LaptopShop.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("LaptopShop.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LaptopShop.Models.ApplicationUser", b =>
                {
                    b.Navigation("CartItems");

                    b.Navigation("Orders");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("LaptopShop.Models.Brand", b =>
                {
                    b.Navigation("Laptops");
                });

            modelBuilder.Entity("LaptopShop.Models.Category", b =>
                {
                    b.Navigation("Laptops");
                });

            modelBuilder.Entity("LaptopShop.Models.Laptop", b =>
                {
                    b.Navigation("CartItems");

                    b.Navigation("OrderDetails");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("LaptopShop.Models.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });
#pragma warning restore 612, 618
        }
    }
}
