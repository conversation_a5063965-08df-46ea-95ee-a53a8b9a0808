{"GlobalPropertiesHash": "gWy3lzi6xDFtmJ1tZF+aF/YzjPRXdacpNHAL4b1RlZI=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["o8oIlFxDBwrLQuFX8guTFlbdh3XwIJrSyUGhxn+Svx8=", "WKcOgpr7l3ddQhV9Ugv2CgHctpgkHNsF1kDOzbMKRzU=", "LAbcV99ElRXv5xoqWtpIYNXBkX+kGdjKh+opTuNr7+4=", "TqnGo8afGChRL9KPXxa3Bcai2JjfZcb7cPu4Hvqkh5c=", "czfFEeM7d2kkaVNllmejo5Fkr20CsqQPtYn7ugBs9Hc=", "h1Z7ty0nJUcBec9O+PZi9+huqRRtZluVAkCtSUNERWM=", "l0QWsV8qZvDBzHE1GYYtGgTwinFsXBgE+Wv341S5gsk=", "vjku2xYrVFgftLA30Gz9vTxUJ829Kfgh3qL82SNYaKo=", "lFdUGzBv54FA9oUi2Y7KO4rJmdXXulBn/ksechyYmEc=", "ZjrO1t+m0yiaz3J5rM1WjxP6HwDGsWQt/RzzX9TAXlI=", "tt3UDEAhIHMoefTotZyzJytptDkdxgCa2aARN9LGun4=", "yfA0m4p9aJOLe6Ib4IAe5IuXrU22QI3/HsCrA/SGSdY=", "79FWNFQeXywIVnpGRWm8eYAbTOrFMt1QTB0SMJo6JSo=", "3AtS8lxV6RPIczQAle8SzbJK61QDvLIt5l0SVCJgfB8=", "EGbwoDPuJ331GXc4tm6DkuhMFbvAwgJtFj36NlbrG0U=", "pi3vkQbprRdGQAXeM8uO6GCJlRB7yfH6OTYItKeKUyg=", "dLJ6rNoO0okDEphIgQhJZgQvXmLFSwSKSamurwZzUzY=", "b7e0k9mESLDbYf7w+TJIz3L9zN/yAkqP58osQ72PDzk=", "GD85inHlXof3Oute2haDClZKGCQ/mWY5Ko2TlRTGzjI=", "sMAaL6NTCcEG+PTF3k+1m5h0WZGKbUg6kUNz9PRje+o=", "3pvE5DDUwquaDsb0YIu2vT82aFs3xYACkG9fslk4eOw=", "AkJk4euTqjpyfc1Bu/mVmOLmwRbaGP65UtHAay+jFgo=", "osd/rQJZrUfkaLhrVghE2K9M43p8qhiyJNct2ptnUY8=", "Z8SDCT9W6ojr72xLTy6myMoxWwosfXGbYxXqyrKUFDk=", "D2nnfa4Ez6mQEATChFA2M2D2v0qNBABGE7N/AsjbUFw=", "SfXhw3Mac/fdStIH43MWQ+plfjr81BBvotI3QT78s04=", "uTUwetT87ID969Z5LImjfp+I0ptf1rVt/eAHIX6+4Qg=", "cvrxFuXE1lE3NtABevBMaMIPtOuvExss8P9SyNKDslo=", "pxwFOGMW7TGFF0/2pCbxIJNsyzurS2JxqmH1nosyGw8=", "6lD9/NEuXrsmDYphWAZ/mfRSpbtspGv3C1Q0OcjaYE8=", "giHwwGl7m4VrLCxeNvcl15REeLAeQEeB+h/tEdfWbEw=", "ITMarS5EsEs9/E/z3VGdePDVDqLsjvQ2xJTUvGZPrbE=", "2kM0B3J+1DHbYXLumv+N1oRJ3i385ZZdTy8yPA0uYrw=", "yQY9aDzJinW0Y2ZeczmsXoNkVOb3tfrh698D9S0mUb4=", "Iy4hYQzza1WTqwX7MkTxTobaKNZeV648xopP60jeCHg=", "tJ1IyIqk1ptX0gAhs/KgJbZ+lZjpdyXYUSfS3NgHETI=", "qcbzUmN9tJjt2eQF/CFlUrtSgUNQvE25JbnC6bzu7gU=", "AEWYB7VgW1N0H8/AxtZtQ6yidZy7bk9Sxug+aqHQqsg=", "MMTYSmhpqddXipoN0nrpg2b2T2aP+/HTVUX7DwBrByo=", "kaK3XBKzB/qeLupJpu4ptFcWBEH+otS5M3c4m0twlqU=", "2jBJEW3SlMmaQwac1Z2HySB8NKI6JsfCRWoaQUQlgag=", "e71Qxq1NgLdYSrkXIfLsmdgxBQFSMypKV9GM1dms4Jg=", "YvzSTWu3e2KhlaaNv99KX9MjfOeJCGy+py3Fle2lc28=", "FNLhSTuMtm0/tknRm6mLig1FUGEzfEXneK4QIrmY/UU=", "FU/7YfSpbM9V+eKkyPJ7aKFKVh8dm79+MJla8vmyum4=", "bzmltYtSv4/YCngCXDjyXKAIuMNQiOpBsy5vAHuI2sE=", "Jns6ULW1Wz+120PP5gaEZkT/wQ2G7NwvmPf4rO931Ac=", "pIEa/8gyfSBjVDug7eQ7GLtIDKD1oGddHgbrNNZ+cMQ=", "JYhnxggKFzk6ymNe9hymt4K22yLYz2RJm2qHbUyVFRA=", "vpqWRCuPF9OknLXuXuDeNHoXoxWAzZHBWDUpvHRdhFw=", "a1oix+Aws+PXuHGn/N47izj2vgdossJJUExXvN0qOro=", "KrsHMk0Dfask6N4oXE8vxpZXPYjddwc3GAANW1j+j0w=", "w+NFTHnaXEoNJM5/xQ1uvjNeA65nOC2OcKEwor9fhBw=", "thsu/7ynArSHWNbQZCMeeD8+z2N1XoUUHb8YNFalN10=", "JcabSVnzI7hAW/jHCneFsxiW6TQD6H7UhN8bzSOS6IE=", "zotXZ4a2ZZm/ewTj466vbe/RFx0Xx1jStsVbSid2TdA=", "k0aBJWOrPXboAbwMrMxO4l21xrrHeQVx7p5wC28hSLg=", "EGMIJ55Ig16N0svhykrzGhcHFU9VSZmtR22B3o4R/6M=", "SU6iDzc3ew5jK3ZMkev4Fn4obfz6NsgSBbAjs+yQez0=", "mOUCtWzhOeQ3TqvfcgESi4mlfDYhA03NRM7eJPonLSo=", "baCO/SC3WllfPDdE2evvFGIBj7GbLUnVzFohFwYQxTw=", "BOxE8KpTUJlvVsCgQNFSbK8EX6OQjcm4A+p/YuVRrw8=", "53KnENUaeE1sdLiIXJLI+ZbUWkGUiiGkyT6lqGd1pZs=", "hT2oF58ULKbGlIkzxyFzpuyImSscYw8s8MrZp3nwcb8=", "QKOkjrEm7n8gZQh3/jWv4Ko/JqlBJKB7Fv0gsaO0go0=", "LR5ns7wvVwcYZk2GbJln2iwOl4UAYSi6lokFf7Ph7u8=", "AUkLgBtdWV246px44sZK8BBxc/WIAlS7kB9SJcbaTXE=", "T+T5onVRpA5qQH6e3n6a3mLHQxl686mXFNJWHa6TA/g=", "Sqb+tkk6Xon4C9NVfFo7FMLC7kC4HRqYxQRfs+5K+ig=", "Sreur+dtrAnZhWeRFOq7youuIn1aGw2TthPGPYKYga0=", "ehkbgGMr0IlXmmhdnV+7m9H8zTkwQ+b3vw3bNeA2Rr4=", "EHsqnsv9wk2qwPLfOSM31dzAARuguzw8XBYqwq4htw4=", "l80Ddh2/SfByK5IOq6bvnK4mVsiK40boa/wm1lFKiQg=", "S5l0wkPFxFxLYFdXF1pMU9nf6rWyd/DIdawRDf6nIJg=", "+oFkMJuVqD915kGlsafpcIGMQNG+5GxRT64glSmEApg=", "781Ha59aD4blRljk8f8VPOOP9gDWAvvx0rPLwl2ftOU=", "jgW2QUzE26kaPFrfqg39GQHYztFCNZXjzWbQgx7hHsQ=", "uL3BzrHYPLULRMR2ogsdJeAKlP3Yie5CqIxuRaE3mCE="], "CachedAssets": {"o8oIlFxDBwrLQuFX8guTFlbdh3XwIJrSyUGhxn+Svx8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sh82cr0ebb", "Integrity": "gBC9UOG5e2K6qrZSiYMV4Ic8vPgrgoSJvr/T1Np/u70=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 8443, "LastWriteTime": "2025-07-16T17:06:17.9428142+00:00"}, "WKcOgpr7l3ddQhV9Ugv2CgHctpgkHNsF1kDOzbMKRzU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-16T16:41:35.5482588+00:00"}, "LAbcV99ElRXv5xoqWtpIYNXBkX+kGdjKh+opTuNr7+4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-16T16:41:35.5803637+00:00"}, "TqnGo8afGChRL9KPXxa3Bcai2JjfZcb7cPu4Hvqkh5c=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-16T16:41:35.4810034+00:00"}, "czfFEeM7d2kkaVNllmejo5Fkr20CsqQPtYn7ugBs9Hc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-16T16:41:35.483004+00:00"}, "h1Z7ty0nJUcBec9O+PZi9+huqRRtZluVAkCtSUNERWM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-16T16:41:35.483004+00:00"}, "l0QWsV8qZvDBzHE1GYYtGgTwinFsXBgE+Wv341S5gsk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-16T16:41:35.483004+00:00"}, "vjku2xYrVFgftLA30Gz9vTxUJ829Kfgh3qL82SNYaKo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-16T16:41:35.4840036+00:00"}, "lFdUGzBv54FA9oUi2Y7KO4rJmdXXulBn/ksechyYmEc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-16T16:41:35.4850401+00:00"}, "ZjrO1t+m0yiaz3J5rM1WjxP6HwDGsWQt/RzzX9TAXlI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-16T16:41:35.4860457+00:00"}, "tt3UDEAhIHMoefTotZyzJytptDkdxgCa2aARN9LGun4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-16T16:41:35.4870468+00:00"}, "yfA0m4p9aJOLe6Ib4IAe5IuXrU22QI3/HsCrA/SGSdY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-16T16:41:35.4870468+00:00"}, "79FWNFQeXywIVnpGRWm8eYAbTOrFMt1QTB0SMJo6JSo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-16T16:41:35.4870468+00:00"}, "3AtS8lxV6RPIczQAle8SzbJK61QDvLIt5l0SVCJgfB8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-16T16:41:35.4880489+00:00"}, "EGbwoDPuJ331GXc4tm6DkuhMFbvAwgJtFj36NlbrG0U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-16T16:41:35.4880489+00:00"}, "pi3vkQbprRdGQAXeM8uO6GCJlRB7yfH6OTYItKeKUyg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-16T16:41:35.4880489+00:00"}, "dLJ6rNoO0okDEphIgQhJZgQvXmLFSwSKSamurwZzUzY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-16T16:41:35.4890472+00:00"}, "b7e0k9mESLDbYf7w+TJIz3L9zN/yAkqP58osQ72PDzk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-16T16:41:35.4890472+00:00"}, "GD85inHlXof3Oute2haDClZKGCQ/mWY5Ko2TlRTGzjI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-16T16:41:35.490048+00:00"}, "sMAaL6NTCcEG+PTF3k+1m5h0WZGKbUg6kUNz9PRje+o=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-16T16:41:35.490048+00:00"}, "3pvE5DDUwquaDsb0YIu2vT82aFs3xYACkG9fslk4eOw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-16T16:41:35.4910446+00:00"}, "AkJk4euTqjpyfc1Bu/mVmOLmwRbaGP65UtHAay+jFgo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-16T16:41:35.4910446+00:00"}, "osd/rQJZrUfkaLhrVghE2K9M43p8qhiyJNct2ptnUY8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-16T16:41:35.4920464+00:00"}, "Z8SDCT9W6ojr72xLTy6myMoxWwosfXGbYxXqyrKUFDk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-16T16:41:35.4920464+00:00"}, "D2nnfa4Ez6mQEATChFA2M2D2v0qNBABGE7N/AsjbUFw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-16T16:41:35.4930455+00:00"}, "SfXhw3Mac/fdStIH43MWQ+plfjr81BBvotI3QT78s04=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-16T16:41:35.4930455+00:00"}, "uTUwetT87ID969Z5LImjfp+I0ptf1rVt/eAHIX6+4Qg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-16T16:41:35.4940462+00:00"}, "cvrxFuXE1lE3NtABevBMaMIPtOuvExss8P9SyNKDslo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-16T16:41:35.4940462+00:00"}, "pxwFOGMW7TGFF0/2pCbxIJNsyzurS2JxqmH1nosyGw8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-16T16:41:35.4952499+00:00"}, "6lD9/NEuXrsmDYphWAZ/mfRSpbtspGv3C1Q0OcjaYE8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-16T16:41:35.4962504+00:00"}, "giHwwGl7m4VrLCxeNvcl15REeLAeQEeB+h/tEdfWbEw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-16T16:41:35.4972509+00:00"}, "ITMarS5EsEs9/E/z3VGdePDVDqLsjvQ2xJTUvGZPrbE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-16T16:41:35.4982495+00:00"}, "2kM0B3J+1DHbYXLumv+N1oRJ3i385ZZdTy8yPA0uYrw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-16T16:41:35.4992498+00:00"}, "yQY9aDzJinW0Y2ZeczmsXoNkVOb3tfrh698D9S0mUb4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-16T16:41:35.4992498+00:00"}, "Iy4hYQzza1WTqwX7MkTxTobaKNZeV648xopP60jeCHg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-16T16:41:35.5002495+00:00"}, "tJ1IyIqk1ptX0gAhs/KgJbZ+lZjpdyXYUSfS3NgHETI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-16T16:41:35.5012489+00:00"}, "qcbzUmN9tJjt2eQF/CFlUrtSgUNQvE25JbnC6bzu7gU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-16T16:41:35.5022493+00:00"}, "AEWYB7VgW1N0H8/AxtZtQ6yidZy7bk9Sxug+aqHQqsg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-16T16:41:35.5022493+00:00"}, "MMTYSmhpqddXipoN0nrpg2b2T2aP+/HTVUX7DwBrByo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-16T16:41:35.5037539+00:00"}, "kaK3XBKzB/qeLupJpu4ptFcWBEH+otS5M3c4m0twlqU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-16T16:41:35.5047592+00:00"}, "2jBJEW3SlMmaQwac1Z2HySB8NKI6JsfCRWoaQUQlgag=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-16T16:41:35.5057593+00:00"}, "e71Qxq1NgLdYSrkXIfLsmdgxBQFSMypKV9GM1dms4Jg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-16T16:41:35.5057593+00:00"}, "YvzSTWu3e2KhlaaNv99KX9MjfOeJCGy+py3Fle2lc28=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-16T16:41:35.5067593+00:00"}, "FNLhSTuMtm0/tknRm6mLig1FUGEzfEXneK4QIrmY/UU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-16T16:41:35.5067593+00:00"}, "FU/7YfSpbM9V+eKkyPJ7aKFKVh8dm79+MJla8vmyum4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-16T16:41:35.50876+00:00"}, "bzmltYtSv4/YCngCXDjyXKAIuMNQiOpBsy5vAHuI2sE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-16T16:41:35.50876+00:00"}, "Jns6ULW1Wz+120PP5gaEZkT/wQ2G7NwvmPf4rO931Ac=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-16T16:41:35.5097618+00:00"}, "pIEa/8gyfSBjVDug7eQ7GLtIDKD1oGddHgbrNNZ+cMQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-16T16:41:35.4840036+00:00"}, "JYhnxggKFzk6ymNe9hymt4K22yLYz2RJm2qHbUyVFRA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-16T16:41:35.5512609+00:00"}, "vpqWRCuPF9OknLXuXuDeNHoXoxWAzZHBWDUpvHRdhFw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-16T16:41:35.552258+00:00"}, "a1oix+Aws+PXuHGn/N47izj2vgdossJJUExXvN0qOro=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-16T16:41:35.4860457+00:00"}, "KrsHMk0Dfask6N4oXE8vxpZXPYjddwc3GAANW1j+j0w=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-16T16:41:35.5442482+00:00"}, "w+NFTHnaXEoNJM5/xQ1uvjNeA65nOC2OcKEwor9fhBw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-16T16:41:35.5482588+00:00"}, "thsu/7ynArSHWNbQZCMeeD8+z2N1XoUUHb8YNFalN10=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-16T16:41:35.5492596+00:00"}, "JcabSVnzI7hAW/jHCneFsxiW6TQD6H7UhN8bzSOS6IE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-16T16:41:35.5492596+00:00"}, "zotXZ4a2ZZm/ewTj466vbe/RFx0Xx1jStsVbSid2TdA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-16T16:41:35.4850401+00:00"}, "k0aBJWOrPXboAbwMrMxO4l21xrrHeQVx7p5wC28hSLg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-16T16:41:35.5107611+00:00"}, "EGMIJ55Ig16N0svhykrzGhcHFU9VSZmtR22B3o4R/6M=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-16T16:41:35.5117599+00:00"}, "SU6iDzc3ew5jK3ZMkev4Fn4obfz6NsgSBbAjs+yQez0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-16T16:41:35.5198009+00:00"}, "mOUCtWzhOeQ3TqvfcgESi4mlfDYhA03NRM7eJPonLSo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-16T16:41:35.5208018+00:00"}, "baCO/SC3WllfPDdE2evvFGIBj7GbLUnVzFohFwYQxTw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-16T16:41:35.5218007+00:00"}, "BOxE8KpTUJlvVsCgQNFSbK8EX6OQjcm4A+p/YuVRrw8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-16T16:41:35.5417419+00:00"}, "53KnENUaeE1sdLiIXJLI+ZbUWkGUiiGkyT6lqGd1pZs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-16T16:41:35.4850401+00:00"}}, "CachedCopyCandidates": {}}